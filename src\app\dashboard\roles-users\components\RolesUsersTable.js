import React from "react";
import { DataTable } from "@globalicons/enterprise-tools";
import { Users } from "lucide-react";
import {
  ButtonGroup,
  FilterButton,
  TableContent,
  LoaderContainer,
  LoaderSpinner,
  LoaderText,
  EmptyState,
  EmptyStateIcon,
  EmptyStateText,
} from "../styles/RolesUsers.styled";

const RolesUsersTable = ({
  currentUserRole,
  activeFilter,
  setActiveFilter,
  isLoading,
  error,
  filteredData,
  columns,
  setSelectedAdministrator,
  setOpenPeakDrawer,
}) => {
  const handleRowClick = (row) => {
    setOpenPeakDrawer(false);
    setSelectedAdministrator(row);
    setOpenPeakDrawer(true);
  };

  return (
    <>
      <ButtonGroup>
        {currentUserRole === "regional_administrator" ? (
          // Show these tabs only for regional administrators
          <>
            <FilterButton
              $active={activeFilter === "regional"}
              onClick={() => setActiveFilter("regional")}
            >
              Regional
            </FilterButton>
            <FilterButton
              $active={activeFilter === "regionalDirector"}
              onClick={() => setActiveFilter("regionalDirector")}
            >
              Regional Director
            </FilterButton>
            <FilterButton
              $active={activeFilter === "nssOfficers"}
              onClick={() => setActiveFilter("nssOfficers")}
            >
              NSS Officers
            </FilterButton>
          </>
        ) : (
          // Show original tabs for other users
          <>
            <FilterButton
              $active={activeFilter === "national"}
              onClick={() => setActiveFilter("national")}
            >
              National
            </FilterButton>
            <FilterButton
              $active={activeFilter === "regional"}
              onClick={() => setActiveFilter("regional")}
            >
              Regional
            </FilterButton>
          </>
        )}
      </ButtonGroup>

      <TableContent>
        {isLoading ? (
          <LoaderContainer>
            <LoaderSpinner />
            <LoaderText>Loading administrators...</LoaderText>
          </LoaderContainer>
        ) : error ? (
          <div
            style={{
              color: "#ef4444",
              padding: "1rem",
              textAlign: "center",
            }}
          >
            Error loading administrators: {error.message}
          </div>
        ) : !filteredData || filteredData.length === 0 ? (
          <EmptyState>
            <EmptyStateIcon>
              <Users size={64} color="#9ca3af" />
            </EmptyStateIcon>
            <EmptyStateText>
              {activeFilter === "national"
                ? "No national administrators found"
                : "No regional administrators found"}
            </EmptyStateText>
          </EmptyState>
        ) : (
          <DataTable
            columns={columns}
            data={filteredData}
            pagination
            highlightOnHover
            responsive
            theme="nss"
            enableColumnVisibility
            enableRowSelection
            enablePagination
            enableFilters
            isSearchable
            pageSize={10}
            isDownloadable={{ formats: ["csv", "pdf"] }}
            onRowClick={handleRowClick}
          />
        )}
      </TableContent>
    </>
  );
};

export default RolesUsersTable;
