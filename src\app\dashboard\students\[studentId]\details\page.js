"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queries, mutations } from "@/api/client";
import { DataTable } from "@globalicons/enterprise-tools";
import {
  ChevronLeft,
  Download,
  Printer,
  ChevronRight,
  InboxIcon,
  X,
  Search,
  Building2,
  MoreVertical,
} from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

// Add these styled components for the image popup and subsection headers

const ImagePopupOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  cursor: pointer;
`;

const ImagePopupContent = styled.div`
  position: relative;
  max-width: 80%;
  max-height: 80%;
`;

const ClosePopupButton = styled.button`
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const PopupImage = styled.img`
  max-width: 100%;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
`;

// START: New Styled Components for Deferment Section
const DefermentStatusTabs = styled.div`
  display: flex;
  padding: 8px;
  background-color: #f0f2f5;

  border-radius: 8px;
  margin-bottom: 24px;
  margin-top: 16px; /* Added margin-top for spacing from SectionHeader */
`;

const TabButton = styled.button`
  padding: 8px 16px;
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
  color: #525866; /* text-gray-300 for inactive tabs on black bg */
  background-color: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  margin-right: 8px;
  transition: all 0.2s ease-in-out;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    background-color: #ffffff; /* White background for any active tab */
    font-weight: 600;
  }

  &.active.pending {
    color: #10b981; /* Tailwind green-500 */
  }
  &.active.approved {
    color: #10b981; /* Tailwind green-500 */
  }
  &.active.rejected {
    color: #ef4444; /* Tailwind red-500 */
  }
`;

const DefermentContentLayout = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
`;

const DefermentInfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
`;

const DefermentTextContent = styled.div`
  flex-grow: 1;
`;

const DefermentInfoBlock = styled.div`
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
`;

const DefermentInfoLabel = styled.p`
  font-size: 0.875rem; /* 14px */
  color: #6b7280; /* text-gray-500 */
  margin-bottom: 4px;
`;

const DefermentInfoValue = styled.p`
  font-size: 1rem; /* 16px */
  color: #1f2937; /* text-gray-800 */
  font-weight: 500;
  white-space: pre-wrap; /* To respect newlines in details */
`;

const DefermentActionButtons = styled.div`
  display: flex;
  flex-direction: row; /* Stack buttons vertically if needed, or row */
  gap: 10px; /* Spacing between buttons */
  margin-left: 24px; /* Space from the text content */
  align-items: flex-start; /* Align to start if stacked */
`;

// Assuming ApproveButton and RejectButton are already defined and styled.
// If not, you might need to style them, e.g.:
// const ApproveButton = styled.button` /* ...styles for green button... */ `;
// const RejectButton = styled.button` /* ...styles for red button... */ `;

// Assuming DocumentItem and its child components (DocumentIconContainer, DocumentIcon, DocumentName, DownloadButton)
// are already defined from your previous structure.
// The DocumentLabel might also be useful.
const DocumentLabel = styled.p`
  font-size: 0.875rem; // 14px
  color: #6b7280; // text-gray-500
  margin-bottom: 8px;
  font-weight: 500;
`;

const ModalButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
`;

const DefermentModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const DefermentModalContent = styled.div`
  background: white;
  padding: 24px;
  border-radius: 8px;
  width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const DefermentModalTitle = styled.h3`
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 1.125rem;
`;
// END: New Styled Components for Deferment Section

export default function StudentDetails() {
  const params = useParams();
  const router = useRouter();
  const { push } = useRouter();
  const studentId = params.studentId;
  const [studentWithDeferment, setStudentWithDeferment] = useState(null);
  const [showPostingModal, setShowPostingModal] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [filteredCompanies, setFilteredCompanies] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isPosting, setIsPosting] = useState(false);
  const [showImagePopup, setShowImagePopup] = useState(false);
  const [activeDefermentStatus, setActiveDefermentStatus] = useState("PENDING");
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);
  const [selectedDeferment, setSelectedDeferment] = useState(null);
  const [showDefermentModal, setShowDefermentModal] = useState(false);

  const defermentColumns = [
    {
      id: "defermentReason",
      accessorKey: "defermentReason",
      header: "Deferment Reason",
    },
    {
      id: "details",
      accessorKey: "details",
      header: "Additional Explanation",
      cell: (info) => {
        const text = info.getValue();
        return (
          <div
            title={text} // Show full text on hover
            style={{
              maxWidth: "300px",
              whiteSpace: "nowrap", // Keep text on a single line
              overflow: "hidden", // Hide overflow
              textOverflow: "ellipsis", // Add ellipsis for overflow
              cursor: "default", // Optional: change cursor to indicate it's not directly editable
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      filterType: "select",
      cell: (info) => {
        const status = info.getValue();
        let color;

        switch (status.toLowerCase()) {
          case "approved":
            color = "#16a34a";
            break;
          case "rejected":
            color = "#ef4444";
            break;
          default:
            color = "#f59e0b";
        }

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
          </span>
        );
      },
    },
    {
      id: "supportingDocuments",
      accessorKey: "supportingDocuments",
      header: "Documents",
      cell: (info) => {
        const documents = info.getValue();
        if (!documents || documents.length === 0) return "No documents";

        return (
          <div style={{ display: "flex", gap: "8px" }}>
            {documents.map((doc, index) => (
              <button
                key={index}
                onClick={(e) => {
                  // e.stopPropagation(); // No longer needed if onRowClick is removed or not primary
                  alert(`Download for ${doc} - URL construction needed.`);
                }}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "4px",
                  padding: "4px 8px",
                  border: "1px solid #e5e7eb",
                  borderRadius: "4px",
                  background: "white",
                  cursor: "pointer",
                }}
              >
                <Download size={16} />
                PDF
              </button>
            ))}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <button
          onClick={() => {
            setSelectedDeferment(row.original);
            setShowDefermentModal(true);
          }}
          style={{
            background: "none",
            border: "none",
            padding: "0", // Remove padding
            color: "#16a34a", // Blue color to look like a link
            textDecoration: "none", // Underline to look like a link
            cursor: "pointer",
            fontSize: "14px", // Match other table text
            fontWeight: "500",
          }}
        >
          View Details
        </button>
      ),
    },
  ];

  const {
    data: studentData,
    isLoading,
    error,
  } = useQuery({
    ...queries.getStudent(studentId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Add companies API query
  const {
    data: companiesData,
    isLoading: isCompaniesLoading,
    error: companiesError,
  } = useQuery({
    ...queries.listCompanies,
    onError: (error) => {
      console.error("API Error:", error);
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Replace mock companies with API data
  useEffect(() => {
    if (companiesData) {
      console.log("Full companies response:", companiesData);

      // Check if data is directly in companiesData (not in .data property)
      const dataSource = Array.isArray(companiesData)
        ? companiesData
        : companiesData.companies && Array.isArray(companiesData.companies)
        ? companiesData.companies
        : []; // Fallback to empty array if no valid data found

      const companiesWithSlots = dataSource.map((company) => ({
        id: company._id, // Use the actual MongoDB _id
        name: company.name || "Unknown Company",
        region: company.region || "Unknown Region",
        slots: company.slots || Math.floor(Math.random() * 10) + 1, // Use actual slots if available
      }));

      console.log("Adapted companies:", companiesWithSlots);

      setCompanies(companiesWithSlots);
      setFilteredCompanies(companiesWithSlots);
    }
  }, [companiesData]);

  useEffect(() => {
    if (studentData?.data) {
      const baseStudentData = { ...studentData.data };
      if (
        baseStudentData.defermentRequests &&
        baseStudentData.defermentRequests.length > 0
      ) {
        baseStudentData.defermentInfo = baseStudentData.defermentRequests[0];
      } else {
        baseStudentData.defermentInfo = null;
      }
      setStudentWithDeferment(baseStudentData);
    }
  }, [studentData]);

  // Use the enhanced student data instead of directly using studentData.data
  const student = studentWithDeferment || studentData?.data;

  // Derive the deferment to display based on the active tab
  const displayDeferment = studentData?.data?.defermentRequests?.find(
    (deferment) => deferment.status === activeDefermentStatus
  );

  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredCompanies(companies);
    } else {
      const filtered = companies.filter(
        (company) =>
          company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          company.region.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCompanies(filtered);
    }
  }, [searchTerm, companies]);

  const handlePostPersonnel = () => {
    setShowPostingModal(true);
  };

  const handleCloseModal = () => {
    setShowPostingModal(false);
    setSelectedCompany(null);
    setSearchTerm("");
    setFilteredCompanies(companies);
  };

  const handleSelectCompany = (company) => {
    setSelectedCompany(company);
  };

  const queryClient = useQueryClient();

  const { mutate: approveDefermentMutate } = useMutation({
    ...mutations.approveDeferment, // Ensure this mutation is defined in your API client
    onSuccess: () => {
      alert("Deferment application approved!");
      queryClient.invalidateQueries(["student", studentId]);
    },
    onError: (error) => {
      alert("Failed to approve deferment. Please try again.");
      console.error("Deferment approval error:", error);
    },
  });

  const { mutate: rejectDefermentMutate } = useMutation({
    ...mutations.rejectDeferment, // Ensure this mutation is defined in your API client
    onSuccess: () => {
      alert("Deferment application rejected!");
      queryClient.invalidateQueries(["student", studentId]);
    },
    onError: (error) => {
      alert("Failed to reject deferment. Please try again.");
      console.error("Deferment rejection error:", error);
    },
  });

  // Add the postStudentToCompany mutation
  const { mutate: postStudentToCompany } = useMutation({
    ...mutations.postStudentToCompany,
    onSuccess: () => {
      handleCloseModal();
      alert(
        `Successfully posted ${student?.firstName} ${student?.lastName} to ${selectedCompany.name}`
      );
      queryClient.invalidateQueries(["student", studentId]);
    },
    onError: (error) => {
      alert("Failed to post personnel. Please try again.");
      console.error("Posting error:", error);
      setIsPosting(false);
    },
  });

  const handleConfirmPosting = async () => {
    if (!selectedCompany) return;

    setIsPosting(true);

    try {
      postStudentToCompany({
        studentId: student._id, // Use _id instead of id
        companyId: selectedCompany.id,
      });
    } catch (error) {
      alert("Failed to post personnel. Please try again.");
      console.error("Posting error:", error);
      setIsPosting(false);
    }
  };

  const defermentRequests = studentData?.data?.defermentRequests || [];
  const pendingCount = defermentRequests.filter(
    (req) => req.status === "PENDING"
  ).length;
  const approvedCount = defermentRequests.filter(
    (req) => req.status === "APPROVED"
  ).length;
  const rejectedCount = defermentRequests.filter(
    (req) => req.status === "REJECTED"
  ).length;

  if (isLoading || isCompaniesLoading) {
    return (
      <Container>
        <LoaderContainer>
          <LoaderSpinner />
          <LoaderText>Loading personnel details...</LoaderText>
        </LoaderContainer>
      </Container>
    );
  }

  if (error || companiesError) {
    return <div>Error: {(error || companiesError)?.message}</div>;
  }

  // const handleDefermentAction = async (action, defermentId) => {
  //   if (!defermentId) {
  //     toast.error("No deferment selected for action.");
  //     return;
  //   }

  //   if (action === "approve") {
  //     try {
  //       const approvalData = {
  //         // Example: You might need to pass specific data for approval
  //         // status: "APPROVED",
  //         // remarks: "Approved after review."
  //       };
  //       await mutations.approveDeferment.mutationFn(defermentId, approvalData);
  //       queryClient.invalidateQueries(["student", studentId]);
  //       queryClient.invalidateQueries(["studentWithDeferment", studentId]);
  //       toast.success("Deferment approved successfully!");
  //     } catch (error) {
  //       console.error("Error approving deferment:", error);
  //       toast.error(
  //         error.response?.data?.message ||
  //           "Failed to approve deferment. Please try again."
  //       );
  //     }
  //   } else if (action === "reject") {
  //     try {
  //       const rejectionData = {
  //         rejectedReason: "Deferment rejected by administrator.",
  //       };
  //       await mutations.rejectDeferment.mutationFn(defermentId, rejectionData);
  //       queryClient.invalidateQueries(["student", studentId]);
  //       queryClient.invalidateQueries(["studentWithDeferment", studentId]);
  //       toast.success("Deferment rejected successfully!");
  //     } catch (error) {
  //       console.error("Error rejecting deferment:", error);
  //       toast.error(
  //         error.response?.data?.message ||
  //           "Failed to reject deferment. Please try again."
  //       );
  //     }
  //   }
  // };
  const handleDefermentAction = async (action, defermentIdToActOn) => {
    // console.log("student", studentWithDeferment); // studentWithDeferment might not be the one with the deferment if list is long
    if (!defermentIdToActOn) {
      toast.error("No specific deferment ID provided for action.");
      return;
    }
    // const defermentId = studentWithDeferment.defermentRequests?.[0]._id; // OLD WAY - Don't use this
    const defermentId = defermentIdToActOn; // NEW WAY

    if (action === "approve") {
      try {
        const approvalData = {};
        await mutations.approveDeferment.mutationFn(defermentId, approvalData);
        queryClient.invalidateQueries(["student", studentId]);
        toast.success("Deferment approved successfully!");
      } catch (error) {
        console.error("Error approving deferment:", error);
        toast.error(
          error.response?.data?.message ||
            "Failed to approve deferment. Please try again."
        );
      }
    } else if (action === "reject") {
      try {
        const rejectionData = {
          rejectedReason: "Deferment rejected by administrator.",
        };
        await mutations.rejectDeferment.mutationFn(defermentId, rejectionData);
        queryClient.invalidateQueries(["student", studentId]);
        toast.success("Deferment rejected successfully!");
      } catch (error) {
        console.error("Error rejecting deferment:", error);
        toast.error(
          error.response?.data?.message ||
            "Failed to reject deferment. Please try again."
        );
      }
    }
    // Close the main details modal as well after action, if desired
    setShowDefermentModal(false);
    setSelectedDeferment(null);
  };

  const isStudentPosted =
    student?.posting?.company?.name ||
    (student?.match?.status && student?.match?.status !== "pending");

  console.log("Student posting status:", {
    isStudentPosted,
    enrollmentStatus: student?.enrollmentStatus,
    matchStatus: student?.match?.status,
    shouldShowButton:
      !isStudentPosted &&
      student?.enrollmentStatus?.toLowerCase() === "awaiting posting",
  });

  return (
    <Container>
      <ActionContainer>
        <PageTitle>View Personnel Details</PageTitle>
        <ButtonGroup>
          <ActionButton
            variant="text"
            onClick={() => {
              window.print();
            }}
          >
            <Printer size={16} />
            Print Page
          </ActionButton>
          {!isStudentPosted &&
            student?.enrollmentStatus?.toLowerCase() === "awaiting posting" && (
              <ActionButton variant="primary" onClick={handlePostPersonnel}>
                <Building2 size={16} />
                Post Personnel
              </ActionButton>
            )}
          <ActionButton
            variant="view"
            onClick={() => {
              // Add navigation logic for attendance view
              push(`/dashboard/students/time-tracking`);
            }}
          >
            View Attendance
            <ChevronRight size={16} />
          </ActionButton>
        </ButtonGroup>
      </ActionContainer>
      <BackButtonContainer>
        <BackButton onClick={() => router.back()}>
          <ChevronLeft size={16} />
          Back
        </BackButton>
      </BackButtonContainer>

      <MainContent>
        <SectionHeader>UPLOADED DETAILS</SectionHeader>

        <InstitutionHeaderSection>
          <InstitutionLogo>
            {/* Replace Next.js Image with standard img tag and add onClick handler */}
            <div style={{ position: "relative", display: "inline-block" }}>
              <img
                src={student?.passportPhoto || "/profile-pic-placeholder.svg"}
                alt="Student Photo"
                width={80}
                height={80}
                style={{ objectFit: "contain", cursor: "pointer" }}
                onClick={() => setShowImagePopup(true)}
              />
              <div
                style={{
                  position: "absolute",
                  bottom: "5px",
                  right: "5px",
                  backgroundColor: "rgba(0,0,0,0.6)",
                  color: "white",
                  borderRadius: "50%",
                  width: "20px",
                  height: "20px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  fontSize: "12px",
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  style={{ cursor: "pointer" }} // Add cursor pointer to SVG
                  onClick={() => setShowImagePopup(true)}
                >
                  <path d="M15 3h6v6M9 21H3v-6M21 3l-7 7M3 21l7-7"></path>
                </svg>
              </div>
            </div>
          </InstitutionLogo>
          <InstitutionHeaderInfo>
            <InstitutionLabel>Student Name</InstitutionLabel>
            <InstitutionName>
              {`${student?.firstName || "John "} ${student?.middleName || ""} ${
                student?.lastName || "Doe"
              }`}
            </InstitutionName>
          </InstitutionHeaderInfo>
        </InstitutionHeaderSection>

        <SectionHeader>OTHER DETAILS</SectionHeader>
        <DetailsGrid>
          <DetailItem>
            <DetailLabel>Student ID</DetailLabel>
            <DetailValue>{student?.studentId || "<EMAIL>"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Gender</DetailLabel>
            <DetailValue>{student?.gender || "Male/Female"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Marital Status</DetailLabel>
            <DetailValue>{student?.maritalStatus || ""}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Residential Address</DetailLabel>
            <DetailValue>
              {student?.residentialAddress || "Home Address"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Residential Region</DetailLabel>
            <DetailValue>
              {student?.residentialRegion || "Home Region"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Ghana GPS Digital Address</DetailLabel>
            <DetailValue>
              {student?.ghanaGPSDigitalAddress || "Digital Address N/A"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Institution Attended</DetailLabel>
            <DetailValue>
              {student?.institution || "<EMAIL>"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Student Batch</DetailLabel>
            <DetailValue>{student?.batch || "Greenhill, Accra"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Student Ghana Card Number</DetailLabel>
            <DetailValue>
              {student?.ghanaCardNumber || "GHA-123456789-0"}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Student Phone Number</DetailLabel>
            <DetailValue>
              {student?.phoneNumber || "Greenhill, Accra"}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Student Email</DetailLabel>
            <DetailValue>
              {student?.emailAddress || "<EMAIL>"}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Course</DetailLabel>
            <DetailValue>
              {student?.course?.name || "Greenhill, Accra"}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>NSS Number</DetailLabel>
            <DetailValue>{student?.nssNumber || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Enrollment Status</DetailLabel>
            <DetailValue>
              {student?.enrollmentStatus
                ? (() => {
                    const status = student.enrollmentStatus.toLowerCase();
                    let color;

                    switch (status) {
                      case "awaiting posting":
                        color = "#16a34a"; // green
                        break;
                      case "awaiting registration":
                        color = "#f59e0b"; // amber
                        break;
                      case "details submitted":
                        color = "#6b7280"; // gray
                        break;
                      case "deferred":
                        color = "#dc2626"; // red
                        break;
                      default:
                        color = "#3b82f6"; // blue for other statuses
                    }

                    return (
                      <span
                        style={{
                          color: color,
                          fontWeight: 500,
                          padding: "0.25rem 0.5rem",
                          borderRadius: "0.25rem",
                          backgroundColor: `${color}15`,
                        }}
                      >
                        {student.enrollmentStatus.charAt(0).toUpperCase() +
                          student.enrollmentStatus.slice(1).toLowerCase()}
                      </span>
                    );
                  })()
                : "N/A"}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Company Posted To </DetailLabel>
            <DetailValue>
              {student?.posting?.company?.name ? (
                (() => {
                  const companyName = student.posting.company.name;
                  const isPending = companyName.toLowerCase() === "pending";

                  const color = isPending ? "#f59e0b" : "#16a34a"; // amber for pending, green for assigned

                  return (
                    <span
                      style={{
                        color: color,
                        fontWeight: 500,
                        padding: "0.25rem 0.5rem",
                        borderRadius: "0.25rem",
                        backgroundColor: `${color}15`,
                      }}
                    >
                      {companyName}
                    </span>
                  );
                })()
              ) : (
                <span
                  style={{
                    color: "#f59e0b", // amber for pending state
                    fontWeight: 500,
                    padding: "0.25rem 0.5rem",
                    borderRadius: "0.25rem",
                    backgroundColor: "#f59e0b15",
                  }}
                >
                  Pending
                </span>
              )}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Region Posted To </DetailLabel>

            <DetailValue>
              {student?.posting?.company?.region ? (
                (() => {
                  const region = student.posting.company.region;
                  const isPending = region.toLowerCase() === "pending";

                  const color = isPending ? "#f59e0b" : "#3b82f6"; // amber for pending, blue for assigned region

                  return (
                    <span
                      style={{
                        color: color,
                        fontWeight: 500,
                        padding: "0.25rem 0.5rem",
                        borderRadius: "0.25rem",
                        backgroundColor: `${color}15`,
                      }}
                    >
                      {region}
                    </span>
                  );
                })()
              ) : (
                <span
                  style={{
                    color: "#f59e0b", // amber for pending state
                    fontWeight: 500,
                    padding: "0.25rem 0.5rem",
                    borderRadius: "0.25rem",
                    backgroundColor: "#f59e0b15",
                  }}
                >
                  Pending
                </span>
              )}
            </DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Date Registered</DetailLabel>
            <DetailValue>
              {student?.dateRegistered
                ? new Date(student.dateRegistered).toISOString().split("T")[0]
                : "N/A"}
            </DetailValue>
          </DetailItem>
        </DetailsGrid>

        <SectionHeader>GUARDIAN</SectionHeader>
        <DetailsGrid>
          <DetailItem>
            <DetailLabel>Guardian Name</DetailLabel>
            <DetailValue>{student?.guardianName || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Guardian Email</DetailLabel>
            <DetailValue>{student?.guardianEmail || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Guardian Phone Number</DetailLabel>
            <DetailValue>{student?.guardianPhoneNumber || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Next of Kin Name</DetailLabel>
            <DetailValue>{student?.nextOfKinName || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Next of Kin Phone Number</DetailLabel>
            <DetailValue>{student?.nextOfKinPhoneNumber || "N/A"}</DetailValue>
          </DetailItem>

          <DetailItem>
            <DetailLabel>Next of Kin Relationship</DetailLabel>
            <DetailValue>{student?.nextOfKinRelationship || "N/A"}</DetailValue>
          </DetailItem>
        </DetailsGrid>

        <SectionHeader>LANGUAGES</SectionHeader>

        <DetailsGrid>
          {student?.localLanguagesRead &&
          student.localLanguagesRead.length > 0 ? (
            <DetailItem>
              <DetailLabel>Languages Read</DetailLabel>
              <DetailValue>{student.localLanguagesRead.join(", ")}</DetailValue>
            </DetailItem>
          ) : null}

          {student?.localLanguagesSpeak &&
          student.localLanguagesSpeak.length > 0 ? (
            <DetailItem>
              <DetailLabel>Languages Spoken</DetailLabel>
              <DetailValue>
                {student.localLanguagesSpeak.join(", ")}
              </DetailValue>
            </DetailItem>
          ) : null}

          {student?.localLanguagesWrite &&
          student.localLanguagesWrite.length > 0 ? (
            <DetailItem>
              <DetailLabel>Languages Written</DetailLabel>
              <DetailValue>
                {student.localLanguagesWrite.join(", ")}
              </DetailValue>
            </DetailItem>
          ) : null}

          {(!student?.localLanguagesRead ||
            student.localLanguagesRead.length === 0) &&
            (!student?.localLanguagesSpeak ||
              student.localLanguagesSpeak.length === 0) &&
            (!student?.localLanguagesWrite ||
              student.localLanguagesWrite.length === 0) && (
              <EmptyStateContainer>
                <EmptyStateText>No local languages specified</EmptyStateText>
              </EmptyStateContainer>
            )}

          {student?.foreignLanguagesRead &&
          student.foreignLanguagesRead.length > 0 ? (
            <DetailItem>
              <DetailLabel> Foreign Languages Read</DetailLabel>
              <DetailValue>
                {student.foreignLanguagesRead.join(", ")}
              </DetailValue>
            </DetailItem>
          ) : null}

          {student?.foreignLanguagesSpeak &&
          student.foreignLanguagesSpeak.length > 0 ? (
            <DetailItem>
              <DetailLabel> Foreign Languages Spoken</DetailLabel>
              <DetailValue>
                {student.foreignLanguagesSpeak.join(", ")}
              </DetailValue>
            </DetailItem>
          ) : null}

          {student?.foreignLanguagesWrite &&
          student.foreignLanguagesWrite.length > 0 ? (
            <DetailItem>
              <DetailLabel>Foreign Languages Written</DetailLabel>
              <DetailValue>
                {student.foreignLanguagesWrite.join(", ")}
              </DetailValue>
            </DetailItem>
          ) : null}
        </DetailsGrid>

        <SectionHeader>PREFERRED INDUSTRIES</SectionHeader>
        {student?.preferredIndustries &&
        student.preferredIndustries.length > 0 ? (
          <DetailsGrid>
            {student.preferredIndustries.map((industry, index) => (
              <DetailItem key={`industry-${index}`}>
                <DetailLabel>Industry {index + 1}</DetailLabel>
                <DetailValue>{industry}</DetailValue>
              </DetailItem>
            ))}
          </DetailsGrid>
        ) : (
          <EmptyStateContainer>
            <EmptyStateIcon>
              <InboxIcon size={64} color="#9ca3af" />
            </EmptyStateIcon>
            <EmptyStateText>No preferred industries found</EmptyStateText>
          </EmptyStateContainer>
        )}

        <SectionHeader>PREFERRED REGIONS</SectionHeader>
        {student?.preferredRegions && student.preferredRegions.length > 0 ? (
          <DetailsGrid>
            {student.preferredRegions.map((region, index) => (
              <DetailItem key={`region-${index}`}>
                <DetailLabel>Region {index + 1}</DetailLabel>
                <DetailValue>{region}</DetailValue>
              </DetailItem>
            ))}
          </DetailsGrid>
        ) : (
          <EmptyStateContainer>
            <EmptyStateIcon>
              <InboxIcon size={64} color="#9ca3af" />
            </EmptyStateIcon>
            <EmptyStateText>No preferred regions found</EmptyStateText>
          </EmptyStateContainer>
        )}

        <SectionHeader>EMPLOYMENT STATUS</SectionHeader>
        <DocumentSection>
          <DetailsGrid>
            <DetailItem>
              <DetailLabel>Currently Employed ?</DetailLabel>
              <DetailValue>
                {student?.currentlyEmployed ? "Yes" : "No"}
              </DetailValue>
            </DetailItem>

            {student?.currentlyEmployed && (
              <>
                <DetailItem>
                  <DetailLabel>Employers Address</DetailLabel>
                  <DetailValue>{student?.employerAddress}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Job Title</DetailLabel>
                  <DetailValue>{student?.jobTitle}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Employer Name</DetailLabel>
                  <DetailValue>{student?.employerName}</DetailValue>
                </DetailItem>
                <DetailItem>
                  <DetailLabel>Employment Start Date</DetailLabel>
                  <DetailValue>
                    {student?.employmentStartDate
                      ? new Date(student.employmentStartDate)
                          .toISOString()
                          .split("T")[0]
                      : "N/A"}
                  </DetailValue>
                </DetailItem>
              </>
            )}
          </DetailsGrid>
        </DocumentSection>

        <SectionHeader>PREVIOUS SERVICE STATUS</SectionHeader>
        <DocumentSection>
          <DetailsGrid>
            <DetailItem>
              <DetailLabel>Previously Done Service ?</DetailLabel>
              <DetailValue>
                {student?.hasDonePreviousService ? "Yes" : "No"}
              </DetailValue>
            </DetailItem>

            <DetailItem>
              {student?.hasDonePreviousService && (
                <>
                  <DetailLabel>Previous NSS Number</DetailLabel>
                  <DetailValue>{student?.previousNSSNumber}</DetailValue>
                </>
              )}
            </DetailItem>
          </DetailsGrid>
        </DocumentSection>

        <SectionHeader>MILITARY STATUS</SectionHeader>
        <DocumentSection>
          <DetailsGrid>
            <DetailItem>
              <DetailLabel>Received Military Training ?</DetailLabel>
              <DetailValue>
                {student?.hasReceivedMilitaryTraining ? "Yes" : "No"}
              </DetailValue>
            </DetailItem>

            <DetailItem>
              {student?.hasReceivedMilitaryTraining && (
                <>
                  <DetailLabel>Military Training Details</DetailLabel>
                  <DetailValue>{student?.militaryTrainingDetails}</DetailValue>
                </>
              )}
            </DetailItem>
          </DetailsGrid>
        </DocumentSection>

        <SectionHeader>STUDENT CONDITION</SectionHeader>
        <DocumentSection>
          <DetailsGrid>
            <DetailItem>
              <DetailLabel>Are You Pregnant ?</DetailLabel>
              <DetailValue>{student?.pregnant ? "Yes" : "No"}</DetailValue>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Physical Disabilities</DetailLabel>
              <DetailValue>
                {student?.physicalDisabilities ? "Yes" : "No"}
              </DetailValue>
            </DetailItem>

            <DetailItem>
              <DetailLabel>Other Medical Conditions</DetailLabel>
              <DetailValue>
                {student?.otherMedicalConditions ? "Yes" : "No"}
              </DetailValue>
            </DetailItem>

            <DetailItem>
              {student?.otherMedicalConditions && (
                <>
                  <DetailLabel>Other Medical Conditions Details</DetailLabel>
                  <DetailValue>
                    {student?.otherMedicalConditionsDetails}
                  </DetailValue>
                </>
              )}
            </DetailItem>

            {student?.conditionDocuments && (
              <>
                <DocumentLabel>Supporting Medical Document</DocumentLabel>
                <DocumentItem>
                  <DocumentIconContainer>
                    <DocumentIcon>PDF</DocumentIcon>
                  </DocumentIconContainer>
                  <DocumentName>
                    {student?.conditionDocuments
                      ? "Medical-Document.pdf"
                      : "Condition-Document.pdf"}
                  </DocumentName>
                  <DownloadButton
                    onClick={() =>
                      student?.conditionDocuments &&
                      window.open(student.conditionDocuments, "_blank")
                    }
                    style={{
                      cursor: student?.conditionDocuments
                        ? "pointer"
                        : "not-allowed",
                      opacity: student?.conditionDocuments ? 1 : 0.5,
                    }}
                  >
                    <Download size={16} />
                  </DownloadButton>
                </DocumentItem>
              </>
            )}
          </DetailsGrid>
        </DocumentSection>

        {/* Only show the Deferment section if there are deferment requests */}
        {studentData?.data?.defermentRequests &&
          studentData.data.defermentRequests.length > 0 && (
            <>
              <SectionHeader>DEFERMENT APPLICATION</SectionHeader>

              <DefermentStatusTabs>
                <TabButton
                  className={`${
                    activeDefermentStatus === "PENDING" ? "active pending" : ""
                  }`}
                  onClick={() => setActiveDefermentStatus("PENDING")}
                >
                  Pending ({pendingCount})
                </TabButton>
                <TabButton
                  className={`${
                    activeDefermentStatus === "APPROVED"
                      ? "active approved"
                      : ""
                  }`}
                  onClick={() => setActiveDefermentStatus("APPROVED")}
                >
                  Approved ({approvedCount})
                </TabButton>
                <TabButton
                  className={`${
                    activeDefermentStatus === "REJECTED"
                      ? "active rejected"
                      : ""
                  }`}
                  onClick={() => setActiveDefermentStatus("REJECTED")}
                >
                  Rejected ({rejectedCount})
                </TabButton>
              </DefermentStatusTabs>

              <DataTable
                data={defermentRequests.filter(
                  (req) => req.status === activeDefermentStatus
                )}
                columns={defermentColumns}
                theme="nss"
                enableColumnVisibility
                enablePagination
                enableFilters
                isSearchable
                isDownloadable={{ formats: ["csv", "pdf"] }}
              />
            </>
          )}
        {/* END OF DEFERMENT APPLICATION SECTION */}
      </MainContent>
      {/* Add Image Popup */}
      {showImagePopup && (
        <ImagePopupOverlay onClick={() => setShowImagePopup(false)}>
          <ImagePopupContent onClick={(e) => e.stopPropagation()}>
            <ClosePopupButton onClick={() => setShowImagePopup(false)}>
              <X size={24} />
            </ClosePopupButton>
            <PopupImage
              src={student?.passportPhoto || "/profile-pic-placeholder.svg"}
              alt="Student Photo"
            />
          </ImagePopupContent>
        </ImagePopupOverlay>
      )}

      {/* Company Posting Modal */}
      {showPostingModal && (
        <ModalOverlay>
          <ModalContainer>
            <ModalHeader>
              <ModalTitle>Post Personnel to Company</ModalTitle>
              <CloseButton onClick={handleCloseModal}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <ModalContent>
              <SearchContainer>
                <SearchIcon>
                  <Search size={16} />
                </SearchIcon>
                <SearchInput
                  type="text"
                  placeholder="Search companies by name or region..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </SearchContainer>

              <CompanyListHeader>
                <CompanyNameHeader>Company Name</CompanyNameHeader>
                <CompanyRegionHeader>Region</CompanyRegionHeader>
                <CompanySlotsHeader>Available Slots</CompanySlotsHeader>
              </CompanyListHeader>

              <CompanyList>
                {filteredCompanies.length > 0 ? (
                  filteredCompanies.map((company) => (
                    <CompanyItem
                      key={company.id}
                      selected={selectedCompany?.id === company.id}
                      onClick={() => handleSelectCompany(company)}
                    >
                      <CompanyName>{company.name}</CompanyName>
                      <CompanyRegion>{company.region}</CompanyRegion>
                      <CompanySlots>{company.slots}</CompanySlots>
                    </CompanyItem>
                  ))
                ) : (
                  <NoCompaniesMessage>
                    No companies match your search criteria
                  </NoCompaniesMessage>
                )}
              </CompanyList>
            </ModalContent>

            <ModalFooter>
              <CancelButton onClick={handleCloseModal}>Cancel</CancelButton>
              <ConfirmButton
                onClick={handleConfirmPosting}
                disabled={!selectedCompany || isPosting}
              >
                {isPosting ? "Posting..." : "Confirm Posting"}
              </ConfirmButton>
            </ModalFooter>
          </ModalContainer>
        </ModalOverlay>
      )}

      {/* Deferment Details Modal */}
      {showDefermentModal && selectedDeferment && (
        <DefermentModalOverlay>
          <DefermentModalContent style={{ width: "600px", maxWidth: "90vw" }}>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "20px",
              }}
            >
              <DefermentModalTitle>Deferment Details</DefermentModalTitle>
              <button
                onClick={() => setShowDefermentModal(false)}
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                  display: "flex",
                }}
              >
                <X size={20} />
              </button>
            </div>

            <DefermentInfoBlock>
              <DefermentInfoLabel>Deferment Reason</DefermentInfoLabel>
              <DefermentInfoValue>
                {selectedDeferment.defermentReason}
              </DefermentInfoValue>
            </DefermentInfoBlock>

            <DefermentInfoBlock>
              <DefermentInfoLabel>Additional Explanation</DefermentInfoLabel>
              <DefermentInfoValue>
                <div
                  style={{
                    maxWidth: "100%",
                    whiteSpace: "normal",
                    wordBreak: "break-word",
                  }}
                >
                  {selectedDeferment.details}
                </div>
              </DefermentInfoValue>
            </DefermentInfoBlock>

            <DefermentInfoBlock>
              <DefermentInfoLabel>Status</DefermentInfoLabel>
              <DefermentInfoValue>
                <span
                  style={{
                    color:
                      selectedDeferment.status.toLowerCase() === "approved"
                        ? "#16a34a"
                        : selectedDeferment.status.toLowerCase() === "rejected"
                        ? "#ef4444"
                        : "#f59e0b",
                    fontWeight: 500,
                    padding: "0.25rem 0.5rem",
                    borderRadius: "0.25rem",
                    backgroundColor: `${
                      selectedDeferment.status.toLowerCase() === "approved"
                        ? "#16a34a"
                        : selectedDeferment.status.toLowerCase() === "rejected"
                        ? "#ef4444"
                        : "#f59e0b"
                    }15`,
                  }}
                >
                  {selectedDeferment.status.charAt(0).toUpperCase() +
                    selectedDeferment.status.slice(1).toLowerCase()}
                </span>
              </DefermentInfoValue>
            </DefermentInfoBlock>

            {selectedDeferment.supportingDocuments &&
              selectedDeferment.supportingDocuments.length > 0 && (
                <DefermentInfoBlock>
                  <DefermentInfoLabel>Supporting Documents</DefermentInfoLabel>
                  <div
                    style={{ display: "flex", gap: "8px", marginTop: "8px" }}
                  >
                    {selectedDeferment.supportingDocuments.map((doc, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          alert(
                            `Download for ${doc} - URL construction needed.`
                          );
                        }}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "4px",
                          padding: "4px 8px",
                          border: "1px solid #e5e7eb",
                          borderRadius: "4px",
                          background: "white",
                          cursor: "pointer",
                        }}
                      >
                        <Download size={16} />
                        PDF
                      </button>
                    ))}
                  </div>
                </DefermentInfoBlock>
              )}

            {selectedDeferment.status === "PENDING" && (
              <ModalButtons>
                <button
                  onClick={() => {
                    setShowConfirmModal(true);
                    setPendingAction("approve");
                    setShowDefermentModal(false);
                  }}
                  style={{
                    padding: "8px 16px",
                    background: "#16a34a15",
                    color: "#16a34a",
                    border: "1px solid #16a34a30",
                    borderRadius: "4px",
                    cursor: "pointer",
                    fontWeight: "500",
                  }}
                >
                  Approve
                </button>
                <button
                  onClick={() => {
                    setShowConfirmModal(true);
                    setPendingAction("reject");
                    setShowDefermentModal(false);
                  }}
                  style={{
                    padding: "8px 16px",
                    background: "#ef444415",
                    color: "#ef4444",
                    border: "1px solid #ef444430",
                    borderRadius: "4px",
                    cursor: "pointer",
                    fontWeight: "500",
                  }}
                >
                  Reject
                </button>
              </ModalButtons>
            )}
          </DefermentModalContent>
        </DefermentModalOverlay>
      )}

      {/* Confirmation Modal for Approve/Reject */}
      {showConfirmModal && selectedDeferment && (
        <DefermentModalOverlay>
          <DefermentModalContent>
            <DefermentModalTitle>
              Are you sure you want to{" "}
              {pendingAction === "approve" ? "approve" : "reject"} this
              deferment request?
            </DefermentModalTitle>
            <p style={{ color: "#6b7280", margin: "0 0 16px 0" }}>
              This action cannot be undone.
            </p>
            <DefermentInfoBlock
              style={{
                padding: "10px",
                background: "#f9fafb",
                borderRadius: "4px",
                marginBottom: "16px",
              }}
            >
              <DefermentInfoLabel style={{ fontSize: "12px" }}>
                Deferment for: {student?.firstName} {student?.lastName}
              </DefermentInfoLabel>
              <DefermentInfoValue style={{ fontSize: "14px" }}>
                Reason: {selectedDeferment.defermentReason}
              </DefermentInfoValue>
            </DefermentInfoBlock>
            <ModalButtons>
              <button
                onClick={() => {
                  setShowConfirmModal(false);
                  setPendingAction(null);
                }}
                style={{
                  padding: "8px 16px",
                  background: "#f3f4f6",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  color: "#374151",
                  fontWeight: "500",
                }}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Pass the specific deferment ID to the action handler
                  handleDefermentAction(pendingAction, selectedDeferment._id);
                  setShowConfirmModal(false);
                  setPendingAction(null);
                }}
                style={{
                  padding: "8px 16px",
                  background:
                    pendingAction === "approve" ? "#16a34a" : "#ef4444",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontWeight: "500",
                }}
              >
                Yes, {pendingAction === "approve" ? "Approve" : "Reject"}
              </button>
            </ModalButtons>
          </DefermentModalContent>
        </DefermentModalOverlay>
      )}
    </Container>
  );
}

const Container = styled.div`
  min-height: 100vh;
  padding-bottom: 2rem;
  margin: 0 auto;
  background-color: #f5f7fa;
`;

const BackButtonContainer = styled.div`
  padding: 1rem 2rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #000;
  padding: 0;
`;

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
  word-break: break-word;
`;
const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DefermentLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DefermentValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 2rem;
`;

const DocumentSection = styled.div`
  margin: 1.5rem 0;
  /* padding: 2rem 1rem; */
  background-color: white;
  border-radius: 4px;
`;

const DocumentItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
`;

const DocumentIconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DocumentIcon = styled.div`
  background-color: #ff4d4f;
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 12px;
  border-radius: 2px;
`;

const DocumentName = styled.div`
  font-size: 16px;
  flex-grow: 1;
`;

const DownloadButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  &:hover {
    color: #000;
  }
`;

const ActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e1e4ea;
`;

const PageTitle = styled.h1`
  font-size: 20px;
  font-weight: 500;
  color: #000;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  ${(props) =>
    props.variant === "view" &&
    `
   border: 1px solid #E2E4E9;
   background-color: #ffffff;
    color: #868C98;
  `}

  ${(props) =>
    props.variant === "text" &&
    `
    background: none;
    border: none;
    color: #119411;
    padding: 0.5rem;
  `}
  
  ${(props) =>
    props.variant === "primary" &&
    `
    background-color: #16a34a;
    border: none;
    color: white;
    
    &:hover {
      background-color: #15803d;
    }
  `}
`;

// Add these styled components after your existing styled components
const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: white;
  border-radius: 4px;
  margin: 1.5rem 0;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1.5rem;
  opacity: 0.7;
`;

const EmptyStateText = styled.div`
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
`;

const DefermentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const DefermentTitle = styled.h3`
  font-size: 18px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DefermentActions = styled.div`
  display: flex;
  gap: 1rem;
`;

const ApproveButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: #16a34a;
  color: white;
  border: none;

  &:hover {
    background-color: #15803d;
  }
`;

const RejectButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  background-color: #ef4444;
  color: white;
  border: none;

  &:hover {
    background-color: #dc2626;
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 4px;

  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
`;

const ModalContent = styled.div`
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
`;

const SearchContainer = styled.div`
  position: relative;
  margin-bottom: 1.5rem;
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
`;

const SearchInput = styled.input`
  width: 92%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  }
`;

const CompanyListHeader = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  border-radius: 6px 6px 0 0;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
`;

const CompanyNameHeader = styled.div``;
const CompanyRegionHeader = styled.div``;
const CompanySlotsHeader = styled.div`
  text-align: center;
`;

const CompanyList = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0 0 6px 6px;
  max-height: 300px;
  overflow-y: auto;
`;

const CompanyItem = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;
  background-color: ${(props) => (props.selected ? "#f0f9ff" : "white")};

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: ${(props) => (props.selected ? "#f0f9ff" : "#f9fafb")};
  }
`;

const CompanyName = styled.div`
  font-weight: 500;
  color: #111827;
`;

const CompanyRegion = styled.div`
  color: #4b5563;
`;

const CompanySlots = styled.div`
  text-align: center;
  font-weight: 500;
  color: #059669;
`;

const NoCompaniesMessage = styled.div`
  padding: 2rem;
  text-align: center;
  color: #6b7280;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
`;

const CancelButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }
`;

const ConfirmButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  background-color: #16a34a;
  color: white;
  cursor: pointer;

  &:hover {
    background-color: #15803d;
  }

  &:disabled {
    background-color: #94d3a2;
    cursor: not-allowed;
  }
`;
