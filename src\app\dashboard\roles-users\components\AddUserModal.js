import React from "react";
import { X, Shield, Globe, MapPin, User, Mail, Phone } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";
import {
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalTitle,
  CloseButton,
  FormContainer,
  FormGroup,
  FormLabel,
  InputWrapper,
  IconWrapper,
  FormInput,
  FormSelect,
  StyledSelect,
  ModalFooter,
  Button,
} from "./AddUserModal.styled";

const AddUserModal = ({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting,
  formData,
  handleInputChange,
}) => {
  // Fetch districts when needed
  const { data: districtsData, isLoading: isLoadingDistricts } = useQuery({
    queryKey: ["districts"],
    queryFn: () => queries.listDistricts({ region: formData.region }).queryFn(),
    enabled: !!formData.region && formData.role === "nss_officer",
    onError: (error) => {
      console.error("Error fetching districts:", error);
    },
  });

  // Function to get available districts
  const getAvailableDistricts = () => {
    if (!districtsData?.data || !Array.isArray(districtsData.data)) return [];
    return districtsData.data.map((district) => ({
      value: district.id,
      label: district.districtName || district.name,
    }));
  };

  // Handle district selection change
  const handleDistrictChange = (selectedOption) => {
    // This would need to be passed from the parent or handled differently
    // For now, we'll create a synthetic event
    const syntheticEvent = {
      target: {
        name: "district",
        value: selectedOption ? selectedOption.value : null,
      },
    };
    handleInputChange(syntheticEvent);
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Add New User</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={18} />
          </CloseButton>
        </ModalHeader>

        <FormContainer onSubmit={onSubmit}>
          <FormGroup fullWidth>
            <FormLabel>Role</FormLabel>
            <InputWrapper>
              <IconWrapper>
                <Shield size={16} />
              </IconWrapper>
              <FormSelect
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Role</option>
                <option value="director_general">Director General</option>
                <option value="regional_administrator">
                  Regional Administrator
                </option>
                <option value="deployment_officer">Deployment Officer</option>
                <option value="head_of_deployment">Head of Deployment</option>
                <option value="regional_director">Regional Director</option>
                <option value="nss_officer">NSS Officer</option>
              </FormSelect>
            </InputWrapper>
          </FormGroup>

          {(formData.role === "regional_administrator" ||
            formData.role === "regional_director" ||
            formData.role === "nss_officer") && (
            <FormGroup fullWidth>
              <FormLabel>Region</FormLabel>
              <InputWrapper>
                <IconWrapper>
                  <Globe size={16} />
                </IconWrapper>
                <FormSelect
                  name="region"
                  value={formData.region}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select a region</option>
                  <option value="Greater Accra Region">
                    Greater Accra Region
                  </option>
                  <option value="Ashanti Region">Ashanti Region</option>
                  <option value="Western Region">Western Region</option>
                  <option value="Eastern Region">Eastern Region</option>
                  <option value="Central Region">Central Region</option>
                  <option value="Volta Region">Volta Region</option>
                  <option value="Northern Region">Northern Region</option>
                  <option value="Upper East Region">Upper East Region</option>
                  <option value="Upper West Region">Upper West Region</option>
                  <option value="North East Region">North East Region</option>
                  <option value="Savannah Region">Savannah Region</option>
                  <option value="Bono Region">Bono Region</option>
                  <option value="Bono East Region">Bono East Region</option>
                  <option value="Ahafo Region">Ahafo Region</option>
                  <option value="Western North Region">
                    Western North Region
                  </option>
                  <option value="Oti Region">Oti Region</option>
                </FormSelect>
              </InputWrapper>
            </FormGroup>
          )}

          {formData.role === "nss_officer" && (
            <FormGroup fullWidth>
              <FormLabel>District</FormLabel>
              <InputWrapper>
                <IconWrapper>
                  <MapPin size={16} />
                </IconWrapper>
                <StyledSelect
                  classNamePrefix="react-select"
                  value={
                    formData.district
                      ? getAvailableDistricts().find(
                          (option) => option.value === formData.district
                        )
                      : null
                  }
                  onChange={handleDistrictChange}
                  options={getAvailableDistricts()}
                  isDisabled={!formData.region || isLoadingDistricts}
                  isLoading={isLoadingDistricts}
                  placeholder={
                    isLoadingDistricts
                      ? "Loading districts..."
                      : "Select or type to search"
                  }
                  isClearable
                  noOptionsMessage={() =>
                    formData.region
                      ? "No districts found for this region"
                      : "Please select a region first"
                  }
                />
              </InputWrapper>
            </FormGroup>
          )}

          <FormGroup>
            <FormLabel>Full Name</FormLabel>
            <InputWrapper>
              <IconWrapper>
                <User size={16} />
              </IconWrapper>
              <FormInput
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                placeholder="Enter full name"
                required
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <FormLabel>Email Address</FormLabel>
            <InputWrapper>
              <IconWrapper>
                <Mail size={16} />
              </IconWrapper>
              <FormInput
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter email address"
                required
              />
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <FormLabel>Phone Number</FormLabel>
            <InputWrapper>
              <IconWrapper>
                <Phone size={16} />
              </IconWrapper>
              <FormInput
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                placeholder="Enter phone number"
                required
              />
            </InputWrapper>
          </FormGroup>

          <ModalFooter>
            <Button
              type="button"
              $secondary
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" $primary disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add User"}
            </Button>
          </ModalFooter>
        </FormContainer>
      </ModalContent>
    </ModalOverlay>
  );
};

export default AddUserModal;
