// src/components/DataTable/SearchBar.js
import React from "react";
import styled from "styled-components";

// Styled Components
const SearchContainer = styled.div`
  position: relative;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;

  &:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
  }

  @media (min-width: 768px) {
    width: 15rem;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 0.625rem;
  color: #9ca3af;
`;

/**
 * SearchBar - Input field for filtering table data
 * @param {Object} props - Component props
 * @param {String} props.value - Current search query
 * @param {Function} props.onChange - Function to call when search query changes
 * @param {String} props.placeholder - Placeholder text for search input
 */
const SearchBar = ({ value, onChange, placeholder }) => {
  return (
    <SearchContainer>
      <SearchInput
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
      <SearchIcon>
        <svg
          style={{ width: "1.25rem", height: "1.25rem" }}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </SearchIcon>
    </SearchContainer>
  );
};

export default SearchBar;
