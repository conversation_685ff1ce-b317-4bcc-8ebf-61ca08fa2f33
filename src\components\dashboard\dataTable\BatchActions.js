import React from "react";
import styled from "styled-components";

const BatchButton = styled.button`
  background-color: ${props => props.selectedCount > 0 ? '#ef4444' : '#9ca3af'};
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: ${props => props.selectedCount > 0 ? 'pointer' : 'not-allowed'};

  &:hover {
    background-color: ${props => props.selectedCount > 0 ? '#dc2626' : '#9ca3af'};
  }
`;

const BatchActions = ({ selectedCount = 0, onActivate }) => {
  return (
    <BatchButton 
      onClick={selectedCount > 0 ? onActivate : undefined}
      selectedCount={selectedCount}
    >
      ACTIVATE BATCH ({selectedCount})
    </BatchButton>
  );
};

export default BatchActions;
