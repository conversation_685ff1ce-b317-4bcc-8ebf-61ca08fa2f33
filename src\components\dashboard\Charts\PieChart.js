import React from "react";
import styled from "styled-components";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>,
  Toolt<PERSON>,
} from "recharts";

const ChartContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
`;

const ChartHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
`;

const ChartIcon = styled.div`
  margin-right: 12px;
  color: #6b7280;
`;

const ChartTitle = styled.h3`
  font-size: 20px;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const LegendContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const LegendLabel = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const LegendColor = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${(props) => props.color};
`;

const LegendText = styled.span`
  font-size: 16px;
  color: #111827;
`;

const LegendValue = styled.span`
  font-size: 16px;
  font-weight: 500;
  color: #111827;
`;

const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
        }}
      >
        <p
          style={{ margin: 0 }}
        >{`${payload[0].name}: ${payload[0].value}%`}</p>
      </div>
    );
  }
  return null;
};

const DefermentRequestsChart = () => {
  const data = [
    { name: "Health-related Issues", value: 52.1, color: "#000000" },
    { name: "Pregnancy", value: 22.8, color: "#7FB3F5" },
    { name: "Already Employed", value: 13.9, color: "#0A9F1C" },
    { name: "Other", value: 11.2, color: "#C5D9F8" },
  ];

  return (
    <ChartContainer>
      <ChartHeader>
        <ChartIcon>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </ChartIcon>
        <ChartTitle>Deferment Requests</ChartTitle>
      </ChartHeader>

      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={100}
            paddingAngle={0}
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
        </PieChart>
      </ResponsiveContainer>

      <LegendContainer>
        {data.map((entry, index) => (
          <LegendItem key={`legend-${index}`}>
            <LegendLabel>
              <LegendColor color={entry.color} />
              <LegendText>{entry.name}</LegendText>
            </LegendLabel>
            <LegendValue>{entry.value}%</LegendValue>
          </LegendItem>
        ))}
      </LegendContainer>
    </ChartContainer>
  );
};

export default DefermentRequestsChart;
