import ChangePassword from "@/app/(auth)/change-password/page";
import axios from "axios";
import toast from "react-hot-toast";

const BASE_URL = "https://qi3mww3uv2.execute-api.eu-west-1.amazonaws.com";
const VERSION = "v1";

const client = axios.create({
  baseURL: BASE_URL,
});

// Add request interceptor to check token and add headers
client.interceptors.request.use(
  (config) => {
    const token = sessionStorage.getItem("authToken");
    if (token) {
      config.headers.Authorization = token;
    }
    config.headers["x-role"] = "administrator";
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Keep existing error handling interceptor
// Improved response interceptor with better unauthorized handling
client.interceptors.response.use(
  (response) => response,
  (error) => {
    handleApiError(error);

    // Handle unauthorized access (401) more robustly
    if (error.response && error.response.status === 401) {
      console.warn("Authentication failed");

      // Check if this is a login attempt or a session expiration
      const isLoginAttempt = error.config.url.includes(
        "/auth/powerusers/login"
      );
      const hasAuthToken = sessionStorage.getItem("authToken");

      if (isLoginAttempt) {
        // This is a failed login attempt, just show error message
        // toast.error("Invalid credentials. Please try again.");
        // Don't redirect - let the login page handle the error
      } else if (hasAuthToken) {
        // This is a session expiration for a logged-in user
        toast.error("Session expired. Please log in again.");

        // Clear auth data
        sessionStorage.removeItem("authToken");

        // Redirect to login page
        if (typeof window !== "undefined") {
          setTimeout(() => {
            window.location.href = "/login";
          }, 0);
        }
      }

      // Prevent further promise chain execution
      return Promise.reject(
        new Error(
          isLoginAttempt
            ? "Invalid credentials"
            : "Unauthorized - session expired"
        )
      );
    }

    return Promise.reject(error);
  }
);

// Keep existing error handler
const handleApiError = (error) => {
  if (error.response) {
    console.error(
      "API Error:",
      error.response.status,
      error.response.data?.error?.message
    );
    // Show appropriate error message based on status code
    const errorMessage =
      error.response.data?.error.message ||
      `Error ${error.response.status}: ${error.response.statusText}`;

    toast.error(errorMessage);
  } else if (error.request) {
    console.error("Network Error: No response received", error.request);
    toast.error("Network error. Please check your connection.");
  } else {
    console.error("Request Error:", error.message);
    toast.error("An error occurred while processing your request.");
  }
};

// API Endpoints
export const api = {
  // Public endpoints
  echo: async (studentName) => {
    const { data } = await client.get(`/${VERSION}/hello/${studentName}`);
    return data;
  },

  createPowerUser: async ({ email, password }) => {
    try {
      const { data } = await client.post(
        `/${VERSION}/powerusers`,
        { email, password },
        {
          headers: {
            "x-admin-key":
              "GH5SW4B-V5DE1HKD7Z2LSCP96DYO5DZID-P665WH441YAT2TBTIS",
          },
        }
      );
      toast.success("Power user created successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  loginPowerUser: async (credentials) => {
    try {
      const { data } = await client.post(
        `/${VERSION}/auth/powerusers/login`,
        credentials
      );
      toast.success("Login successful!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  checkPincode: async (pincodeData) => {
    const { data } = await client.post(
      `/${VERSION}/students/check-pincode`,
      pincodeData
    );
    return data;
  },

  // Protected endpoints
  createBatch: async (batchData) => {
    try {
      const { data } = await client.post(`/${VERSION}/batches`, batchData);
      toast.success("Batch created successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  updateBatch: async (batchId, updateData) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/batches/${batchId}`,
        updateData
      );
      toast.success("Batch updated successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  archiveBatch: async (batchId) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/batches/${batchId}/archive`
      );
      toast.success("Batch archived successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  listBatches: async () => {
    const { data } = await client.get(`/${VERSION}/batches?status=active`);
    return data;
  },

  getBatch: async ({ batchId }) => {
    const { data } = await client.get(`/${VERSION}/batches/${batchId}`);
    return data;
  },

  activateBatch: async (batchId) => {
    const { data } = await client.post(
      `/${VERSION}/batches/${batchId}/activate`
    );
    return data;
  },

  listStudents: async () => {
    const { data } = await client.get(`/${VERSION}/students/all?status=active`);
    return data;
  },
  listInstitutions: async () => {
    const { data } = await client.get(`/${VERSION}/institutions`);
    return data;
  },

  getInstitution: async ({ institutionId }) => {
    const { data } = await client.get(
      `/${VERSION}/institutions/${institutionId}`
    );
    return data;
  },

  approveInstitution: async ({ institutionId, approved, approvalStatus }) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/institutions/${institutionId}/approve`,
        { approved, approvalStatus }
      );
      toast.success(
        `Institution ${approved ? "approved" : "rejected"} successfully!`
      );
      return data;
    } catch (error) {
      throw error;
    }
  },

  listStudentsInBatch: async ({ batchId }) => {
    const { data } = await client.get(
      `/${VERSION}/students/batch/${batchId}?status=active`
    );
    return data;
  },
  listStudentsInInstitution: async ({ institutionId }) => {
    const { data } = await client.get(
      `/${VERSION}/students/institution/${institutionId}?status=active`
    );
    return data;
  },
  activateBatch: async ({
    batchId,
    registrationStartDate,
    registrationEndDate,
  }) => {
    try {
      const { data } = await client.post(
        `/${VERSION}/batches/${batchId}/activate`,
        { registrationStartDate, registrationEndDate }
      );
      toast.success("Batch activated successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },
  getStudent: async ({ studentId }) => {
    const { data } = await client.get(`/${VERSION}/student/${studentId}`);
    return data;
  },

  getCompany: async ({ companyId }) => {
    const { data } = await client.get(`/${VERSION}/companies/${companyId}`);
    return data;
  },

  approveCompany: async ({ companyId, approved, approvalStatus }) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/companies/${companyId}/approve`,
        { approved, approvalStatus }
      );
      toast.success(
        `Company ${approved ? "approved" : "rejected"} successfully!`
      );
      return data;
    } catch (error) {
      throw error;
    }
  },

  listCompanies: async () => {
    const { data } = await client.get(`/${VERSION}/companies`);
    return data.data;
  },

  listPendingCompanies: async () => {
    const { data } = await client.get(
      `/${VERSION}/companies/approvals?status=pending`
    );
    return data.data;
  },

  listArchivedCompanies: async () => {
    const { data } = await client.get(
      `/${VERSION}/companies/approvals?status=rejected`
    );
    return data.data;
  },

  matchStudents: async (batchId) => {
    try {
      const { data } = await client.post(`/${VERSION}/match/${batchId}`);
      toast.success("Students matched successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  postStudentToCompany: async ({ studentId, companyId }) => {
    try {
      const { data } = await client.post(`/${VERSION}/postings/students`, {
        studentId,
        companyId,
      });
      toast.success("Student posted to company successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  getCompanyRequests: async (companyId) => {
    const { data } = await client.get(`/${VERSION}/requests/${companyId}`);
    return data;
  },

  listDeferments: async () => {
    try {
      const { data } = await client.get(`/${VERSION}/deferments`);
      return data.data || data;
    } catch (error) {
      console.error("Error fetching deferments:", error);
      throw error;
    }
  },

  approveDeferment: async (defermentId, approvalData) => {
    try {
      const { data } = await client.put(
        `/${VERSION}/deferments/${defermentId}/approve`,
        approvalData
      );
      toast.success("Deferment approved successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  rejectDeferment: async (defermentId, rejectionData) => {
    try {
      const { data } = await client.put(
        `/${VERSION}/deferments/${defermentId}/reject`,
        rejectionData
      );
      toast.success("Deferment rejected successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  // New endpoint for creating PowerUsers companies
  createPowerUserCompany: async ({
    name,
    email,
    address,
    industry,
    companyType,
    digitalAddress,
    contact,
    TINNumber,
    registrationNumber,
    taxCertificate,
    registrationCertificate,
    region,
  }) => {
    try {
      const { data } = await client.post(`/${VERSION}/powerusers/companies`, {
        name,
        email,
        address,
        industry,
        companyType,
        digitalAddress,
        contact,
        TINNumber,
        registrationNumber,
        taxCertificate,
        registrationCertificate,
        region,
      });
      toast.success("Company created successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  // New function for getting presigned URLs
  getPresignedUrl: async ({ fileName, fileType, uploadType, companyName }) => {
    try {
      const { data } = await client.get(
        `/${VERSION}/company/register/presigned`,
        {
          params: { fileName, fileType, uploadType, companyName },
        }
      );
      return data.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error?.message || "Failed to get presigned URL"
      );
    }
  },

  // New function for uploading files
  uploadFile: async ({ url, file }) => {
    try {
      const { data } = await axios.put(url, file);
      console.log("Upload File Response:", data);
      return data.data;
    } catch (error) {
      throw new Error(
        error.response?.data?.error?.message || "Failed to upload file"
      );
    }
  },

  addAdministrators: async ({
    role,
    fullName,
    phoneNumber,
    email,
    region,
    district,
  }) => {
    try {
      const { data } = await client.post(`/${VERSION}/administrators`, {
        role,
        fullName,
        phoneNumber,
        email,
        region,
        district,
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  listAdministrators: async () => {
    try {
      const { data } = await client.get(`/${VERSION}/administrators`);
      return data;
    } catch (error) {
      throw error;
    }
  },

  getAdministrator: async ({ administratorId }) => {
    try {
      const { data } = await client.get(
        `/${VERSION}/administrators/${administratorId}`
      );
      return data;
    } catch (error) {
      throw error;
    }
  },

  disableAdministrator: async ({ administratorId }) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/administrators/${administratorId}/disable`
      );
      toast.success("Administrator disabled successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  activateAdministrator: async ({ administratorId }) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/administrators/${administratorId}/enable`
      );
      toast.success("Administrator activated successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  changePassword: async ({ currentPassword, newPassword, confirmPassword }) => {
    try {
      const { data } = await client.patch(
        `/${VERSION}/auth/powerusers/users/password`,
        { currentPassword, newPassword, confirmPassword }
      );
      toast.success("Password changed successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  addCenter: async ({ centerName, region, ghanaPostGPS, districtIds }) => {
    try {
      const { data } = await client.post(`/${VERSION}/centers`, {
        centerName,
        region,
        ghanaPostGPS,
        districtIds,
      });
      toast.success("Center added successfully!");
      return data;
    } catch (error) {
      throw error;
    }
  },

  listCenters: async () => {
    try {
      const { data } = await client.get(`/${VERSION}/centers`);
      return data;
    } catch (error) {
      console.error("Error fetching centers:", error);
      throw error;
    }
  },

  listDistricts: async (params) => {
    try {
      const { data } = await client.get(`/${VERSION}/districts`, { params });
      return data;
    } catch (error) {
      console.error("Error fetching districts:", error);
      throw error;
    }
  },
};

// React Query hooks
export const queries = {
  listBatches: {
    queryKey: ["batches"],
    queryFn: api.listBatches,
  },

  getBatch: (batchId) => ({
    queryKey: ["batch", batchId],
    queryFn: () => api.getBatch(batchId),
  }),

  listStudentsInInstitution: {
    queryKey: ["institution"],
    queryFn: ({ institutionId }) =>
      api.listStudentsInInstitution({ institutionId }),
  },
  listStudents: {
    queryKey: ["students"],
    queryFn: api.listStudents,
  },
  listInstitutions: {
    queryKey: ["institutions"],
    queryFn: api.listInstitutions,
  },
  listStudentsInBatch: {
    queryKey: ["studentsInBatch"],
    queryFn: api.listStudentsInBatch,
  },

  listCompanies: {
    queryKey: ["companies"],
    queryFn: api.listCompanies,
  },

  listPendingCompanies: {
    queryKey: ["pendingCompanies"],
    queryFn: api.listPendingCompanies,
  },

  listArchivedCompanies: {
    queryKey: ["archivedCompanies"],
    queryFn: api.listArchivedCompanies,
  },

  getCompany: (companyId) => ({
    queryKey: ["company", companyId],
    queryFn: () => api.getCompany({ companyId }),
  }),

  getStudent: (studentId) => ({
    queryKey: ["student", studentId],
    queryFn: () => api.getStudent({ studentId }),
  }),

  getInstitution: (institutionId) => ({
    queryKey: ["institution", institutionId],
    queryFn: () => api.getInstitution({ institutionId }),
  }),

  getCompanyRequests: (companyId) => ({
    queryKey: ["companyRequests", companyId],
    queryFn: () => api.getCompanyRequests(companyId),
  }),

  listDeferments: {
    queryKey: ["deferments"],
    queryFn: api.listDeferments,
  },

  getDeferment: (defermentId) => ({
    queryKey: ["deferment", defermentId],
    queryFn: () => api.getDeferment(defermentId),
  }),

  // Add this new query
  getDefermentByStudentId: (studentId) => ({
    queryKey: ["deferment", "student", studentId],
    queryFn: () => api.getDefermentByStudentId(studentId),
  }),

  // New query for getting presigned URLs
  getPresignedUrl: (params) => ({
    queryKey: ["getPresignedUrl", params.uploadType],
    queryFn: () => api.getPresignedUrl(params),
    enabled: false, // Disabled by default, manually triggered with refetch
  }),

  listAdministrators: {
    queryKey: ["administrators"],
    queryFn: api.listAdministrators,
  },

  getAdministrator: (administratorId) => ({
    queryKey: ["administrator", administratorId],
    queryFn: () => api.getAdministrator({ administratorId }),
  }),

  listCenters: () => ({
    queryKey: ["centers"],
    queryFn: () => api.listCenters(),
  }),

  listDistricts: (params) => ({
    queryKey: ["districts", params.region],
    queryFn: () => api.listDistricts(params),
  }),
};

// Add mutations object for POST, PATCH, and DELETE operations
export const mutations = {
  createPowerUser: {
    mutationFn: api.createPowerUser,
  },
  createBatch: {
    mutationFn: api.createBatch,
  },
  loginPowerUser: {
    mutationFn: api.loginPowerUser,
  },
  activateBatch: {},

  approveCompany: {
    mutationFn: api.approveCompany,
  },

  approveInstitution: {
    mutationFn: api.approveInstitution,
  },

  matchStudents: {
    mutationFn: api.matchStudents,
  },

  postStudentToCompany: {
    mutationFn: api.postStudentToCompany,
  },

  // New mutation for creating PowerUsers companies
  createPowerUserCompany: {
    mutationFn: api.createPowerUserCompany,
  },

  // New mutation for uploading files
  uploadFile: {
    mutationFn: api.uploadFile,
  },

  approveDeferment: {
    mutationFn: api.approveDeferment,
  },

  rejectDeferment: {
    mutationFn: api.rejectDeferment,
  },

  addAdministrators: {
    mutationFn: api.addAdministrators,
  },

  disableAdministrator: {
    mutationFn: api.disableAdministrator,
  },
  changePassword: {
    mutationFn: api.changePassword,
  },

  activateAdministrator: {
    mutationFn: api.activateAdministrator,
  },
  addCenter: {
    mutationFn: api.addCenter,
  },
};

export default client;
