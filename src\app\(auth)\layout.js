"use client";
import styled from "styled-components";
import { createGlobalStyle } from "styled-components";

// Global style to prevent body scrolling
const GlobalStyle = createGlobalStyle`
  body, html {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
`;

const LayoutWrapper = styled.div`
  min-height: 100vh;
  width: 100%;
  position: relative;
  background-image: url("/images/AuthBackground.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
`;

const Logo = styled.img`
  width: 364px;
  position: absolute;
  top: 32px;
  left: 32px;
`;

const ContentWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
`;

const BottomTextAndImage = styled.div`
  position: absolute;
  bottom: 32px;
  left: 32px;
  right: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Copyright = styled.p`
  display: flex;
  color: white;
  margin: 0;
`;

const RightLogo = styled.img`
  width: 81px;
`;

const AuthLayout = ({ children }) => {
  return (
    <>
      <GlobalStyle />
      <LayoutWrapper>
        <Logo src="/logos/NSAGovLogo.png" alt="Left Logo" />
        <ContentWrapper>{children}</ContentWrapper>
        <BottomTextAndImage>
          <Copyright>
            &copy; {new Date().getFullYear()} National Service Authority. All
            rights reserved.
          </Copyright>
          <RightLogo src="/logos/GovLogo.png" alt="Right Logo" />
        </BottomTextAndImage>
      </LayoutWrapper>
    </>
  );
};

export default AuthLayout;
