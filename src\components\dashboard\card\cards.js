import React from "react";
import styled from "styled-components";
import { ArrowUpRight, ArrowDownRight } from "lucide-react";

const CardContainer = styled.div`
  background-color: #f5f7fa;
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  transition: transform 0.2s, box-shadow 0.2s;

  ${(props) =>
    props.isClickable &&
    `
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  `}
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const DateRange = styled.span`
  font-size: 0.875rem;
  color: #111827;
  font-weight: 500;
`;

const PercentageChange = styled.span`
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  background-color: ${(props) =>
    props.$positive ? "rgba(16, 185, 129, 0.1)" : "rgba(239, 68, 68, 0.1)"};
  color: ${(props) => (props.$positive ? "#10b981" : "#ef4444")};
  font-weight: 500;
`;

const CardTitle = styled.h3`
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
  margin: 0;
`;

const CardContent = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
`;

const CardValue = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
`;

const MiniChart = styled.div`
  display: flex;
  align-items: flex-end;
  height: 35px;
  gap: 2px;
`;

const ChartBar = styled.div`
  width: 5px;
  height: ${(props) => props.$height}%;
  background-color: ${(props) => props.$color};
  border-radius: 1px;
`;

const StatsCard = ({
  title,
  value,
  dateRange,
  percentageChange,
  chartData,
  onClick,
  isClickable = false,
}) => {
  const isPositive = parseFloat(percentageChange) >= 0;

  const handleClick = () => {
    if (isClickable && onClick) {
      onClick();
    }
  };

  return (
    <CardContainer onClick={handleClick} isClickable={isClickable}>
      <CardHeader>
        <DateRange>{dateRange}</DateRange>
        <PercentageChange $positive={isPositive}>
          {isPositive ? "+" : ""}
          {percentageChange}%
        </PercentageChange>
      </CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardContent>
        <CardValue>{value}</CardValue>
        <MiniChart>
          {chartData.map((bar, index) => (
            <ChartBar key={index} $height={bar.height} $color={bar.color} />
          ))}
        </MiniChart>
      </CardContent>
    </CardContainer>
  );
};

export default StatsCard;
