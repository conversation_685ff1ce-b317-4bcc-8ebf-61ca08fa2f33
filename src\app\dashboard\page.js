"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { Calendar, MoreVertical } from "lucide-react";
import StatsCard from "@/components/dashboard/card/cards";
import { DataTable } from "@globalicons/enterprise-tools";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";
import { api } from "@/api/client";
import { useRouter } from "next/navigation";
import DefermentRequestsChart from "@/components/dashboard/Charts/PieChart";
import StudentsSubmittedChart from "@/components/dashboard/Charts/BarChart";

const DashboardContainer = styled.div`
  padding: 1rem;
  background-color: #f9fafb;
  min-height: 100vh;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.875rem;
  font-weight: 600;
  color: #111827;
`;

const DatePickerContainer = styled.div`
  position: relative;
`;

const DatePickerInput = styled.input`
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  width: 200px;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
  }
`;

const CalendarIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
`;

const StatsCardGrid = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  & > * {
    flex: 1 1 220px;
    min-width: 220px;
    max-width: calc(25% - 1.125rem);
  }
`;

const TableContainer = styled.div`
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

// Add these loader components
const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const ChartsContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const Dashboard = () => {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [isLoading, setIsLoading] = useState(true);
  const [institutionsData, setInstitutionsData] = useState([]);
  const [studentsData, setStudentsData] = useState([]);
  const [batchesData, setBatchesData] = useState([]); // Add state for batches
  const [selectedInstitution, setSelectedInstitution] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch institutions
        const institutionsResponse = await api.listInstitutions();
        setInstitutionsData(institutionsResponse.data || []);

        // Fetch students
        const studentsResponse = await api.listStudents();
        setStudentsData(studentsResponse.data || []);

        // Fetch batches
        const batchesResponse = await api.listBatches();
        setBatchesData(batchesResponse.data || []);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
  };

  const handleViewMoreAnalytics = () => {
    router.push("/dashboard/analytics");
  };

  // Sample data for the charts
  const studentChartData = [
    { height: 40, color: "#10b981" },
    { height: 60, color: "#10b981" },
    { height: 30, color: "#10b981" },
    { height: 80, color: "#3b82f6" },
    { height: 50, color: "#ef4444" },
  ];

  const institutionChartData = [
    { height: 35, color: "#3b82f6" },
    { height: 55, color: "#3b82f6" },
    { height: 70, color: "#3b82f6" },
    { height: 60, color: "#3b82f6" },
    { height: 40, color: "#ef4444" },
  ];

  const batchChartData = [
    { height: 30, color: "#3b82f6" },
    { height: 45, color: "#3b82f6" },
    { height: 60, color: "#3b82f6" },
    { height: 75, color: "#10b981" },
    { height: 90, color: "#10b981" },
  ];

  const specialPostingRequests = [
    { height: 50, color: "#3b82f6" },
    { height: 65, color: "#3b82f6" },
    { height: 70, color: "#10b981" },
    { height: 60, color: "#3b82f6" },
    { height: 78, color: "#10b981" },
  ];

  // Define columns configuration for institutions
  const columns = [
    {
      id: "name",
      accessorKey: "name",
      header: "Institution Name",
      filterType: "select",
    },
    {
      id: "email",
      accessorKey: "email",
      header: "Email",
    },
    {
      id: "numberOfStudents",
      accessorKey: "numberOfStudents",
      header: "Number Of Students Submitted",
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: "Registration Date",
      filterType: "date-range",
      cell: (info) => {
        const date = new Date(info.getValue());
        return date.toLocaleDateString();
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left by 100px
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  // minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedInstitution(info.row.original);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleArchive(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                    color: "#ef4444",
                  }}
                >
                  Archive
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <DashboardContainer>
      <Header>
        <DatePickerContainer>
          <CalendarIcon>
            <Calendar size={18} />
          </CalendarIcon>
          <DatePickerInput
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
          />
        </DatePickerContainer>
      </Header>

      <StatsCardGrid>
        <StatsCard
          title="Total Students"
          value={studentsData.length.toString()}
          dateRange="13/08/2025 - 22/08/2025"
          percentageChange="2"
          chartData={studentChartData}
          onClick={() => router.push("/dashboard/students")}
          isClickable={true}
        />

        <StatsCard
          title="Active Institutions"
          value={institutionsData.length.toString()}
          dateRange="13/08/2025 - 22/08/2025"
          percentageChange="-1.5"
          chartData={institutionChartData}
          onClick={() => router.push("/dashboard/institutions")}
          isClickable={true}
        />

        <StatsCard
          title="Active Batches"
          value={batchesData.length.toString()}
          dateRange="13/08/2025 - 22/08/2025"
          percentageChange="5.3"
          chartData={batchChartData}
          onClick={() => router.push("/dashboard/batches")}
          isClickable={true}
        />

        <StatsCard
          title="Total Special Posting Requests"
          value="20"
          dateRange="13/08/2025 - 22/08/2025"
          percentageChange="3.2"
          chartData={specialPostingRequests}
          onClick={() => router.push("/dashboard/students/special-posting")}
          isClickable={true}
        />
      </StatsCardGrid>

      {/* Add the charts section */}
      <ChartsContainer>
        <DefermentRequestsChart />
        <StudentsSubmittedChart onViewMoreClick={handleViewMoreAnalytics} />
      </ChartsContainer>

      <TableContainer>
        <SectionTitle>Recently Registered</SectionTitle>
        {isLoading ? (
          <LoaderContainer>
            <LoaderSpinner />
            <LoaderText>Loading institutions...</LoaderText>
          </LoaderContainer>
        ) : (
          <DataTable
            data={institutionsData}
            columns={columns}
            theme="nss"
            enableColumnVisibility
            enableRowSelection
            enablePagination
            enableFilters
            isDownloadable={{ formats: ["csv", "pdf"] }}
            isSearchable
            // onRowClick={(row) => {
            //   setOpenPeakDrawer(false);
            //   setSelectedInstitution(row);
            //   setOpenPeakDrawer(true);
            // }}
          />
        )}
      </TableContainer>

      <PeakDetails
        institution={selectedInstitution}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />
    </DashboardContainer>
  );
};

export default Dashboard;
