"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";
import { Download } from "lucide-react";

export default function CompanyDetails() {
  const params = useParams();
  const { push } = useRouter();
  const companyId = params.companyId;

  const { data: companyData } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  const company = companyData?.data;

  console.log("Company data:", company);
  console.log("Approval status:", company?.approved);
  console.log("Needs approval:", company?.approved === false);

  return (
    <MainContent>
      <SectionHeader>PROFILE</SectionHeader>

      <InstitutionHeaderSection>
        <InstitutionLogo>
          <Image
            src="/profile-pic-placeholder.svg"
            alt="Company Logo"
            width={80}
            height={80}
            style={{ objectFit: "contain" }}
          />
        </InstitutionLogo>
        <InstitutionHeaderInfo>
          <InstitutionLabel>Company Name</InstitutionLabel>
          <InstitutionName>
            {company?.name || "Global Tech Solutions"}
          </InstitutionName>
        </InstitutionHeaderInfo>
      </InstitutionHeaderSection>

      <SectionHeader>UPLOADED DETAILS</SectionHeader>
      <DetailsGrid>
        <DetailItem>
          <DetailLabel>Company Email</DetailLabel>
          <DetailValue>
            {company?.emailAddress || "<EMAIL>"}
          </DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Company Address</DetailLabel>
          <DetailValue>
            {company?.address || "14 Independence Ave, Accra"}
          </DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Industry</DetailLabel>
          <DetailValue>{company?.industry || "IT and Software"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Phone Number</DetailLabel>
          <DetailValue>{company?.contact || "+233 20 123 4567"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Company Type</DetailLabel>
          <DetailValue>{company?.companyType || "5"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Digital Address</DetailLabel>
          <DetailValue>{company?.digitalAddress || "2023 Batch"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Status</DetailLabel>
          <DetailValue>{company?.approvalStatus || "Active"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Region</DetailLabel>
          <DetailValue>{company?.region || "15/05/2023"}</DetailValue>
        </DetailItem>
      </DetailsGrid>

      <SectionHeader>COMPANY INCORPORATION DOCUMENTS</SectionHeader>
      <DocumentSection>
        <DocumentLabel>Company Incorporation Document</DocumentLabel>
        <DocumentItem>
          <DocumentIconContainer>
            <DocumentIcon>PDF</DocumentIcon>
          </DocumentIconContainer>
          <DocumentName>
            {company?.registrationCertificate
              ? "Company Registration Document.pdf"
              : "company-incorporation.pdf"}{" "}
          </DocumentName>
          <DownloadButton
            onClick={() =>
              company?.registrationCertificate &&
              window.open(company.documents, "_blank")
            }
            style={{
              cursor: company?.registrationCertificate
                ? "pointer"
                : "not-allowed",
              opacity: company?.registrationCertificate ? 1 : 0.5,
            }}
          >
            <Download size={16} />
          </DownloadButton>
        </DocumentItem>

        {/* Tax Certificate Document */}
        <DocumentLabel style={{ marginTop: "1.5rem" }}>
          Tax Certificate
        </DocumentLabel>
        <DocumentItem>
          <DocumentIconContainer>
            <DocumentIcon>PDF</DocumentIcon>
          </DocumentIconContainer>
          <DocumentName>
            {company?.taxCertificate
              ? "Tax Clearance Certificate.pdf"
              : "tax-certificate.pdf"}{" "}
          </DocumentName>
          <DownloadButton
            onClick={() =>
              company?.taxCertificate &&
              window.open(company.taxCertificate, "_blank")
            }
            style={{
              cursor: company?.taxCertificate ? "pointer" : "not-allowed",
              opacity: company?.taxCertificate ? 1 : 0.5,
            }}
          >
            <Download size={16} />
          </DownloadButton>
        </DocumentItem>
      </DocumentSection>
    </MainContent>
  );
}

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0; /* Ensures the flex item can shrink below its minimum content size */
  overflow: hidden; /* Prevents content from overflowing */
`;

const DetailLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
  word-break: break-word; /* Breaks long words to prevent overflow */
  text-overflow: ellipsis; /* Adds ellipsis for overflow text */
  overflow: hidden; /* Hides overflow content */
`;

const DocumentSection = styled.div`
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DocumentLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 1rem;
`;

const DocumentItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
`;

const DocumentIconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DocumentIcon = styled.div`
  background-color: #ff4d4f;
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 12px;
  border-radius: 2px;
`;

const DocumentName = styled.div`
  font-size: 16px;
  flex-grow: 1;
`;

const DownloadButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  &:hover {
    color: #000;
  }
`;
