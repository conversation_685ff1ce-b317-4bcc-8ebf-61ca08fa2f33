"use client";
import { useState, useEffect } from "react";
import { Instrument_Sans } from "next/font/google";
import StyledComponentsRegistry from "./registry";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "styled-components";
import { theme } from "@/constants/theme";
import { createGlobalStyle } from "styled-components";
import { Toaster } from "react-hot-toast";
// Remove NextTopLoader
import NProgress from "nprogress";
import "@/components/styles/dashboard/nprogress-custom.css";
import { usePathname } from "next/navigation"; // Only use pathname, not searchParams

const GlobalStyle = createGlobalStyle`
  body {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    }
`;

const instrumentSans = Instrument_Sans({
  subsets: ["latin"],
  display: "swap",
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 60 * 1000,
      networkMode: "online",
    },
  },
});

export default function RootLayout({ children }) {
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname(); // Only use pathname
  
  // Configure NProgress once
  useEffect(() => {
    NProgress.configure({ showSpinner: false });
  }, []);

  // Handle route changes with pathname only
  useEffect(() => {
    NProgress.done(true);
  }, [pathname]);

  // Handle initial page load
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <title>National Service Secretariat -Back Office Portal</title>
      </head>
      <body className={instrumentSans.className} suppressHydrationWarning>
        <StyledComponentsRegistry>
          <ThemeProvider theme={theme}>
            <QueryClientProvider client={queryClient}>
              <GlobalStyle />
              {mounted ? children : null}
              <Toaster
                position="top-center"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: "#fff",
                    color: "#333",
                  },
                }}
              />
            </QueryClientProvider>
          </ThemeProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
