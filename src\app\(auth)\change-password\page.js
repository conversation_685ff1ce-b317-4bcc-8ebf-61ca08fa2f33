"use client";

import AuthForm from "@/components/form/form";
import { FiLock } from "react-icons/fi";

const ChangePassword = () => {
  const resetFields = [
    {
      name: "password",
      type: "password",
      label: "New Password",
      placeholder: "Enter new password",
      required: true,
      icon: <FiLock />,
      showPasswordField: "showNewPassword", // Add this field
    },
    {
      name: "confirmPassword",
      type: "password",
      label: "Confirm Password",
      placeholder: "Re-enter new password",
      required: true,
      icon: <FiLock />,
      showPasswordField: "showConfirmPassword", // Add this field
    },
  ];

  const links = {
    toggleText: "Back to Login?",
    toggleLabel: "Log In",
    toggleLink: "/login",
    home: "/",
  };

  const handleResetPassword = (formData) => {
    if (formData.password !== formData.confirmPassword) {
      alert("Passwords do not match.");
      return;
    }
    console.log("Reset Password request:", formData);
  };

  return (
    <AuthForm
      title="Reset Password"
      subtitle="Enter a new password to reset your account"
      fields={resetFields}
      links={links}
      onSubmit={handleResetPassword}
      buttonText="Change Password" // Change button text
      isResetPassword={true} // Pass this flag
    />
  );
};

export default ChangePassword;
