// app/dashboard/account/layout.js
"use client";

import { React } from "react";
import SidebarNav from "@/components/dashboard/AccountSidebar";
import {
  <PERSON><PERSON>,
  Container,
  ContentWrapper,
  <PERSON>lexContainer,
  <PERSON><PERSON>,
  MainContent,
  MainTitle,
} from "@/components/styles/dashboard/account.styled";
import { usePathname } from "next/navigation"; // usePathname is used to get the current path
import Link from "next/link";

const AccountLayout = ({ children }) => {
  const pathname = usePathname();
  let title = "Institution Profile"; // Default title

  // Check the pathname and set the title accordingly
  if (pathname.includes("active-sessions")) {
    title = "Active Sessions";
  } else if (pathname.includes("change-password")) {
    title = "Change Password";
  }

  return (
    <Container>
      <ContentWrapper>
        <MainContent>
          <FlexContainer>
            <SidebarNav />
            <div style={{ flex: 1 }}>
              <Header>
                <MainTitle>{title}</MainTitle>
              </Header>
              {children}
            </div>
          </FlexContainer>
        </MainContent>
      </ContentWrapper>
    </Container>
  );
};

export default AccountLayout;
