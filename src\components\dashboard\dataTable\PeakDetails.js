import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { X, Download } from "lucide-react";
import Image from "next/image";
import styled from "styled-components";
import { useRouter } from "next/navigation";

export default function PeakDetails({
  isOpen,
  onClose,
  student,
  institution,
  company,
  administrator,
}) {
  const router = useRouter();
  const isInstitution = !!institution;
  const isCompany = !!company;
  const isAdministrator = !!administrator;
  const data = isCompany
    ? company
    : isInstitution
    ? institution
    : isAdministrator
    ? administrator
    : student;

  return (
    <StyledDialog open={isOpen} onClose={() => onClose()}>
      <Overlay />
      <OverflowHiddenDiv>
        <AbsoluteOverflowHiddenDiv>
          <PanelContainer>
            <StyledDialogPanel transition>
              <PanelContent>
                <PaddedDiv>
                  <HeaderDiv>
                    <StyledDialogTitle>
                      <div className="logo">
                        <Image fill src="/logos/nsa.svg" alt="Logo" />
                      </div>
                      <HeaderTitle>
                        {isCompany
                          ? "Company Details"
                          : isInstitution
                          ? "Institution Details"
                          : isAdministrator
                          ? "Administrator Details"
                          : "Student Details"}
                      </HeaderTitle>
                    </StyledDialogTitle>
                    <CloseButtonContainer>
                      <CloseButton onClick={() => onClose()}>
                        <span className="absolute -inset-2.5" />
                        <span className="sr-only">Close panel</span>
                        <X size={16} aria-hidden="true" />
                      </CloseButton>
                    </CloseButtonContainer>
                  </HeaderDiv>
                </PaddedDiv>
                <ScrollableContent>
                  <ContentArea>
                    <ProfilePic>
                      <div className="profile-pic">
                        <img
                          src={
                            isCompany
                              ? data?.logo || "/profile-pic-placeholder.svg"
                              : isInstitution
                              ? data?.logo || "/profile-pic-placeholder.svg"
                              : isAdministrator
                              ? "/profile-pic-placeholder.svg"
                              : student?.passportPhoto ||
                                "/profile-pic-placeholder.svg"
                          }
                          alt={
                            isCompany
                              ? "Company Logo"
                              : isInstitution
                              ? "Institution Logo"
                              : isAdministrator
                              ? "Administrator Profile"
                              : "Student Profile"
                          }
                          style={{
                            width: "100%",
                            maxHeight: "350px",
                            height: "100%",
                            objectFit: "cover",
                          }}
                        />
                      </div>
                    </ProfilePic>

                    <DetailsSection>
                      <SectionTitle>
                        {isCompany
                          ? "COMPANY DETAILS"
                          : isInstitution
                          ? "INSTITUTION DETAILS"
                          : isAdministrator
                          ? "ADMINISTRATOR DETAILS"
                          : "STUDENT DETAILS"}
                      </SectionTitle>
                      {isCompany ? (
                        // Company Details
                        <>
                          <DetailItem>
                            <Label>COMPANY NAME</Label>
                            <Value>{data?.name}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>COMPANY EMAIL</Label>
                            <Value>{data?.email}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>PHONE NUMBER</Label>
                            <Value>{data?.contact}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>ADDRESS</Label>
                            <Value>{data?.address}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>REGION</Label>
                            <Value>{data?.region}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>DIGITAL ADDRESS</Label>
                            <Value>{data?.digitalAddress}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>STATUS</Label>
                            <Value>{data?.approvalStatus}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>SUPPORTING DOCUMENTS</Label>
                            <DocumentLink>
                              <Icon>
                                <Image fill src="/pdficon.svg" alt="PDF Icon" />
                              </Icon>
                              <DocumentAnchor href={data?.documents} download>
                                {data?.documents}
                              </DocumentAnchor>
                              <Download style={{ marginLeft: "auto" }} />
                            </DocumentLink>
                          </DetailItem>
                        </>
                      ) : isInstitution ? (
                        // Institution Details
                        <>
                          <DetailItem>
                            <Label>INSTITUTION ID</Label>
                            <Value>{data?.id}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>INSTITUTION NAME</Label>
                            <Value>{data?.name}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>LOCATION</Label>
                            <Value>{data?.address}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>TYPE OF INSTITUTION</Label>
                            <Value>{data?.category}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>CONTACT EMAIL</Label>
                            <Value>{data?.email}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>DIGITAL ADDRESS</Label>
                            <Value>{data?.digitalAddress}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>SUPPORTING DOCUMENTS</Label>
                            <DocumentLink>
                              <Icon>
                                <Image fill src="/pdficon.svg" alt="PDF Icon" />
                              </Icon>
                              <DocumentAnchor href={data?.documents} download>
                                {data?.documents}
                              </DocumentAnchor>
                              <Download style={{ marginLeft: "auto" }} />
                            </DocumentLink>
                          </DetailItem>
                        </>
                      ) : isAdministrator ? (
                        // Administrator Details
                        <>
                          <DetailItem>
                            <Label>FULL NAME</Label>
                            <Value>{data?.fullName}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>EMAIL</Label>
                            <Value>{data?.email}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>PHONE NUMBER</Label>
                            <Value>{data?.phoneNumber}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>ROLE</Label>
                            <Value>
                              {data?.role
                                ? data.role
                                    .split("_")
                                    .map(
                                      (word) =>
                                        word.charAt(0).toUpperCase() +
                                        word.slice(1)
                                    )
                                    .join(" ")
                                : ""}
                            </Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>REGION</Label>
                            <Value>{data?.region}</Value>
                          </DetailItem>
                        </>
                      ) : (
                        // Student Details (existing structure)
                        <>
                          <DetailItem>
                            <Label>STUDENT ID</Label>
                            <Value>{data?.studentId}</Value>
                          </DetailItem>
                          <DetailItem>
                            <Label>STUDENT NAME</Label>
                            <Value>
                              {student?.firstName} {student?.middleName}{" "}
                              {student?.lastName}
                            </Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>INSTITUTION ATTENDED</Label>
                            <Value>{student?.institution}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>BATCH</Label>
                            <Value>{student?.batch}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>ENROLLMENT STATUS</Label>
                            <Value>{student?.enrollmentStatus}</Value>
                          </DetailItem>

                          {/* <DetailItem>
                            <Label>SUPPORTING DOCUMENTS</Label>
                            <DocumentLink>
                              <Icon>
                                <Image fill src="/pdficon.svg" alt="PDF Icon" />
                              </Icon>
                              <DocumentAnchor
                                href={student?.documents}
                                download
                              >
                                {student?.documents}
                              </DocumentAnchor>
                              <Download style={{ marginLeft: "auto" }} />
                            </DocumentLink>
                          </DetailItem> */}

                          <DetailItem>
                            <Label>STUDENT PHONE NUMBER</Label>
                            <Value>{student?.phoneNumber}</Value>
                          </DetailItem>

                          <DetailItem>
                            <Label>STUDENT EMAIL</Label>
                            <Value>{student?.emailAddress}</Value>
                          </DetailItem>
                        </>
                      )}
                    </DetailsSection>
                  </ContentArea>
                </ScrollableContent>
                <Footer>
                  <ViewMoreButton
                    onClick={() => {
                      onClose();
                      if (isCompany) {
                        router.push(
                          `/dashboard/companies/${data?._id}/details`
                        );
                      } else if (isInstitution) {
                        router.push(
                          `/dashboard/institutions/${data?._id}/details`
                        );
                      } else if (isAdministrator) {
                        router.push(
                          `/dashboard/roles-users/${data?._id}/details`
                        );
                      } else {
                        router.push(
                          `/dashboard/students/${student?._id}/details`
                        );
                      }
                    }}
                  >
                    VIEW MORE
                  </ViewMoreButton>
                </Footer>
              </PanelContent>
            </StyledDialogPanel>
          </PanelContainer>
        </AbsoluteOverflowHiddenDiv>
      </OverflowHiddenDiv>
    </StyledDialog>
  );
}

const StyledDialog = styled(Dialog)`
  position: relative;
  z-index: 120;
`;

const Overlay = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  backdrop-filter: blur(1.5px);
  transition: backdrop-filter 0.3s ease;
`;

const OverflowHiddenDiv = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
`;

const AbsoluteOverflowHiddenDiv = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
`;

const PanelContainer = styled.div`
  pointer-events: none;
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  display: flex;
  max-width: 100%;
  padding-left: 2.5rem;
`;

const StyledDialogPanel = styled(DialogPanel)`
  pointer-events: auto;
  width: 100vw;
  max-width: 28rem;
  transform: translateX(0);
  transition: transform 500ms ease-in-out;

  border: 1px solid #e0e0e0;
  border-radius: 10px 0 0 10px;
  overflow: hidden;

  &[data-closed] {
    transform: translateX(100%);
  }

  @media (min-width: 640px) {
    transition-duration: 700ms;
  }
`;

const PanelContent = styled.div`
  display: flex;
  height: 100%;
  flex-direction: column;
  background-color: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  > *:not(:first-child) {
    border-top: 1px solid #e5e7eb;
  }
`;

const ScrollableContent = styled.div`
  display: flex;
  min-height: 0;
  flex-direction: column;
  overflow-y: scroll;
  overflow-x: hidden;
`;

const PaddedDiv = styled.div`
  padding: 1rem 20px;
`;

const HeaderDiv = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StyledDialogTitle = styled(DialogTitle)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1em;
  margin: 0;

  & .logo {
    position: relative;

    width: 50px;
    height: 50px;

    & img {
      object-fit: contain;
    }
  }
`;

const CloseButtonContainer = styled.div`
  margin-left: 0.75rem;
  display: flex;
  height: 1.75rem;
  align-items: center;
`;

const CloseButton = styled.button`
  all: unset;

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  background-color: #f6eb16;
  width: 1.2rem;
  height: 1.2rem;
  padding: 6px;
  color: #000;

  & .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  &:hover {
    color: #6b7280;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px #6366f1;
  }
`;

const ContentArea = styled.div`
  position: relative;
  flex: 1 1 0%;
  padding-left: 0;
  padding-right: 0;
`;

const Footer = styled.div`
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  padding: 1rem;
`;

const HeaderTitle = styled.h2`
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px; /* 133.333% */
  color: #0e121b;
  letter-spacing: -0.27px;
`;

const ProfilePic = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding-block: 42px;

  background-color: #fafafa;

  /* & .profile-pic {
    position: relative;
    width: 150px;
    height: 150px;
  } */
`;

const ProfileImage = styled.img`
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
`;

const DetailsSection = styled.div`
  margin-bottom: 20px;
`;

const SectionTitle = styled.h3`
  width: 100%;
  padding: 6px 20px;
  margin-top: 0;
  margin-bottom: 28px;
  gap: 6px;
  background: #f5f7fa;
  color: #99a0ae;

  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.48px;
  text-transform: uppercase;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px 0;
  margin: 12px 20px;
  border-bottom: 1px solid #e1e4ea;

  &:last-child {
    border: none;
  }
`;

const Label = styled.span`
  display: block;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  color: #99a0ae;
  line-height: 16px; /* 133.333% */
  letter-spacing: 0.48px;
`;

const Value = styled.span`
  font-size: 14px;
  font-weight: 500;
  color: #333;
`;

const DocumentLink = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  padding-block: 4px;
`;

const Icon = styled.div`
  position: relative;
  width: 32px;
  height: 32px;

  & img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

const DocumentAnchor = styled.a`
  font-size: 14px;
  color: #333;
  text-decoration: none;
`;

const ViewMoreButton = styled.button`
  all: unset;

  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 40px;

  color: #000;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 106.18%;

  border-radius: 5px;
  border: 1px solid #99a0ae;

  padding: 10px;
  margin: 4px 20px;
  cursor: pointer;

  &:hover {
    background-color: #e0e0e0;
  }
`;
