"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";

export default function RequestPersonnel() {
  const params = useParams();
  const { push } = useRouter();
  const companyId = params.companyId;

  const { data: companyData } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Fetch company requests
  const { data: requestsData, isLoading: isLoadingRequests } = useQuery({
    ...queries.getCompanyRequests(companyId),
    retry: 1,
    onError: (error) => {
      console.error("Error fetching company requests:", error);
    },
    enabled: !!sessionStorage.getItem("authToken") && !!companyId,
  });

  console.log("Requests data:", requestsData);

  // Dummy data for preferred programs
  const dummyPrograms = [
    { name: "Software Engineering" },
    { name: "Data Science" },
    { name: "Cybersecurity" },
    { name: "Digital Marketing" },
    { name: "Project Management" },
    { name: "UI/UX Design" },
    { name: "Cloud Computing" },
    { name: "Business Analytics" },
  ];

  const company = companyData?.data;
  const requestData = requestsData?.data;

  // Format gender ratio percentages from the API response
  const maleRatio = requestData?.genderRatio?.male
    ? `${(requestData.genderRatio.male * 100).toFixed(0)}%`
    : "50%";

  const femaleRatio = requestData?.genderRatio?.female
    ? `${(requestData.genderRatio.female * 100).toFixed(0)}%`
    : "50%";

  // Use real data if available, otherwise use dummy data
  const preferredPrograms =
    requestData?.coursePreferences?.length > 0
      ? requestData.coursePreferences.map((course) => ({ name: course }))
      : company?.preferredPrograms?.length > 0
      ? company.preferredPrograms
      : dummyPrograms;

  return (
    <MainContent>
      <SectionHeader>PROFILE</SectionHeader>

      <InstitutionHeaderSection>
        <InstitutionLogo>
          <Image
            src="/profile-pic-placeholder.svg"
            alt="Company Logo"
            width={80}
            height={80}
            style={{ objectFit: "contain" }}
          />
        </InstitutionLogo>
        <InstitutionHeaderInfo>
          <InstitutionLabel>Company Name</InstitutionLabel>
          <InstitutionName>
            {company?.name || "Global Tech Solutions"}
          </InstitutionName>
        </InstitutionHeaderInfo>
      </InstitutionHeaderSection>

      <SectionHeader>PERSONNEL RATIO</SectionHeader>
      <DetailsGrid>
        <DetailItem>
          <DetailLabel>Number of Personnel&apos;s</DetailLabel>
          <DetailValue>{company?.numberOfPersonnels || "200"}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Ratio Men</DetailLabel>
          <DetailValue>{maleRatio}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Ratio Women</DetailLabel>
          <DetailValue>{femaleRatio}</DetailValue>
        </DetailItem>
      </DetailsGrid>

      <SectionHeader>PREFERRED PROGRAMS</SectionHeader>
      <ProgramsContainer>
        {preferredPrograms && preferredPrograms.length > 0 ? (
          preferredPrograms.map((program, index) => (
            <ProgramCard key={index}>
              <ProgramName>{program.name}</ProgramName>
            </ProgramCard>
          ))
        ) : (
          <EmptyState>
            <EmptyStateText>No preferred programs selected</EmptyStateText>
          </EmptyState>
        )}
      </ProgramsContainer>
    </MainContent>
  );
}

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0; /* Ensures the flex item can shrink below its minimum content size */
  overflow: hidden; /* Prevents content from overflowing */
`;

const DetailLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
  word-break: break-word; /* Breaks long words to prevent overflow */
  text-overflow: ellipsis; /* Adds ellipsis for overflow text */
  overflow: hidden; /* Hides overflow content */
`;

const ProgramsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.25rem;
  margin: 1.5rem 0;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
`;

const ProgramCard = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border-radius: 12px;
  background-color: #f9fafb;
  transition: all 0.3s ease;
  text-align: center;
  height: 20px;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-4px);
    background-color: #f0f9f4;
  }
`;

const ProgramName = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
`;

const EmptyState = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
  grid-column: 1 / -1;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #e1e4ea;
`;

const EmptyStateText = styled.p`
  font-size: 14px;
  color: #666;
  text-align: center;
`;
