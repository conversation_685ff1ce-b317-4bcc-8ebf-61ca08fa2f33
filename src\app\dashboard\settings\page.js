"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import styled from "styled-components";
import { useRouter } from "next/navigation";

// Styled components
const ProfileContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const ProfileCard = styled.div`
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  margin-bottom: 30px;
`;

const ProfileImage = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #f8f8f8;
`;

const ProfileInfo = styled.div`
  margin-left: 20px;
`;

const ProfileName = styled.h2`
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 10px;
`;

const ProfileID = styled.p`
  font-size: 16px;
  color: #666;
`;

const DetailsSection = styled.div`
  margin-top: 20px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  padding: 20px;
`;

const DetailsSectionHeader = styled.h2`
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
`;

const DetailsItem = styled.div`
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailsLabel = styled.p`
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
`;

const DetailsValue = styled.p`
  font-size: 18px;
  font-weight: 500;
`;

const SettingsProfile = () => {
  const router = useRouter();
  const [user, setUser] = useState({
    name: "",
    email: "",
    profileImage: "/profile-pic-placeholder.svg",
    role: "",
  });

  useEffect(() => {
    // Get user data from sessionStorage
    const user = sessionStorage.getItem("user")
      ? JSON.parse(sessionStorage.getItem("user"))
      : null;
    const token = sessionStorage.getItem("authToken");

    if (!token) {
      router.push("/login");
      return;
    }

    if (user.email) {
      const parsedEmail = user.email;
      setUser({
        name: user.fullName,
        email: parsedEmail || "",
        profileImage: "/profile-pic-placeholder.svg",
        role: user.role,
      });
    }
  }, [router]);

  return (
    <ProfileContainer>
      <ProfileCard>
        <ProfileImage>
          <Image
            src={user.profileImage || "/profile-pic-placeholder.svg"}
            alt={user.name}
            width={100}
            height={100}
            objectFit="cover"
          />
        </ProfileImage>

        <ProfileInfo>
          <ProfileName>{user.name}</ProfileName>
          <ProfileID>{user.email}</ProfileID>
        </ProfileInfo>
      </ProfileCard>

      <DetailsSection>
        <DetailsSectionHeader>Account Details</DetailsSectionHeader>

        <DetailsItem>
          <DetailsLabel>Email Address</DetailsLabel>
          <DetailsValue>{user.email}</DetailsValue>
        </DetailsItem>
        <DetailsItem>
          <DetailsLabel>Role</DetailsLabel>
          <DetailsValue>
            {user.role
              ? user.role
                  .split("_")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ")
              : ""}
          </DetailsValue>
        </DetailsItem>
      </DetailsSection>
    </ProfileContainer>
  );
};

export default SettingsProfile;
