"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";
import { useState, useEffect } from "react";
import { Edit2, Trash2 } from "lucide-react";

export default function Notes() {
  const params = useParams();
  const { push } = useRouter();
  const companyId = params.companyId;
  const [noteText, setNoteText] = useState("");
  const [notes, setNotes] = useState([]);
  const [editingNoteId, setEditingNoteId] = useState(null);
  const [editText, setEditText] = useState("");

  const { data: companyData } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  const company = companyData?.data;

  // Mock function to get user info - replace with actual implementation
  const getCurrentUser = () => {
    return {
      name: "John Doe",
      avatar: "/profile-pic-placeholder.svg"
    };
  };

  const handleAddNote = () => {
    if (noteText.trim()) {
      const newNote = {
        id: Date.now(), // temporary ID
        text: noteText,
        date: new Date(),
        user: getCurrentUser(),
      };
      
      setNotes([newNote, ...notes]);
      setNoteText("");
    }
  };

  const handleEditNote = (noteId) => {
    const noteToEdit = notes.find(note => note.id === noteId);
    if (noteToEdit) {
      setEditingNoteId(noteId);
      setEditText(noteToEdit.text);
    }
  };

  const saveEditedNote = () => {
    if (editText.trim()) {
      setNotes(notes.map(note => 
        note.id === editingNoteId 
          ? { ...note, text: editText, date: new Date() } 
          : note
      ));
      setEditingNoteId(null);
      setEditText("");
    }
  };

  const handleDeleteNote = (noteId) => {
    setNotes(notes.filter(note => note.id !== noteId));
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <MainContent>
      <SectionHeader>PROFILE</SectionHeader>

      <InstitutionHeaderSection>
        <InstitutionLogo>
          <Image
            src="/profile-pic-placeholder.svg"
            alt="Company Logo"
            width={80}
            height={80}
            style={{ objectFit: "contain" }}
          />
        </InstitutionLogo>
        <InstitutionHeaderInfo>
          <InstitutionLabel>Company Name</InstitutionLabel>
          <InstitutionName>
            {company?.name || "Global Tech Solutions"}
          </InstitutionName>
        </InstitutionHeaderInfo>
      </InstitutionHeaderSection>

      <SectionHeader>ADD NOTES</SectionHeader>
      <NoteInputSection>
        <NoteInput 
          value={noteText}
          onChange={(e) => setNoteText(e.target.value)}
          placeholder="Type your note here..."
          onKeyDown={(e) => {
            if (e.key === 'Enter' && noteText.trim()) {
              handleAddNote();
            }
          }}
        />
        <AddNoteButton onClick={handleAddNote}>Add Note</AddNoteButton>
      </NoteInputSection>

      <SectionHeader>ALL NOTES</SectionHeader>
      <NotesContainer>
        {notes.length === 0 ? (
          <EmptyNotesMessage>No notes added yet.</EmptyNotesMessage>
        ) : (
          notes.map((note) => (
            <NoteItem key={note.id}>
              <NoteUserAvatar>
                <Image
                  src={note.user.avatar}
                  alt="User Avatar"
                  width={50}
                  height={50}
                  style={{ objectFit: "cover", borderRadius: "50%" }}
                />
              </NoteUserAvatar>
              <NoteContent>
                {editingNoteId === note.id ? (
                  <EditNoteForm>
                    <EditNoteInput
                      value={editText}
                      onChange={(e) => setEditText(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && editText.trim()) {
                          saveEditedNote();
                        }
                      }}
                      autoFocus
                    />
                    <SaveButton onClick={saveEditedNote}>Save</SaveButton>
                  </EditNoteForm>
                ) : (
                  <>
                    <NoteText>{note.text}</NoteText>
                    <NoteMetadata>
                      <NoteDate>{formatDate(note.date)}</NoteDate>
                      <NoteAuthor>by {note.user.name}</NoteAuthor>
                    </NoteMetadata>
                  </>
                )}
              </NoteContent>
              <NoteActions>
                <ActionButton onClick={() => handleEditNote(note.id)}>
                  <Edit2 size={16} />
                </ActionButton>
                <ActionButton onClick={() => handleDeleteNote(note.id)}>
                  <Trash2 size={16} />
                </ActionButton>
              </NoteActions>
            </NoteItem>
          ))
        )}
      </NotesContainer>
    </MainContent>
  );
}

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const NoteInputSection = styled.div`
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background-color: white;
  border-radius: 4px;
`;

const NoteInput = styled.input`
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #119411;
    box-shadow: 0 0 0 2px rgba(17, 148, 17, 0.1);
  }
`;

const AddNoteButton = styled.button`
  background-color: #119411;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #0d730d;
  }
`;

const NotesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 1rem 0;
`;

const EmptyNotesMessage = styled.div`
  padding: 2rem;
  text-align: center;
  color: #6b7280;
  background-color: white;
  border-radius: 4px;
`;

const NoteItem = styled.div`
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const NoteUserAvatar = styled.div`
  flex-shrink: 0;
`;

const NoteContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const NoteText = styled.p`
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
`;

const NoteMetadata = styled.div`
  display: flex;
  gap: 0.5rem;
  font-size: 12px;
  color: #6b7280;
`;

const NoteDate = styled.span``;

const NoteAuthor = styled.span``;

const NoteActions = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: flex-start;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
    color: #111827;
  }
`;

const EditNoteForm = styled.div`
  display: flex;
  gap: 0.5rem;
  width: 100%;
`;

const EditNoteInput = styled.input`
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #119411;
  }
`;

const SaveButton = styled.button`
  background-color: #119411;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 12px;
  cursor: pointer;
  
  &:hover {
    background-color: #0d730d;
  }
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;
