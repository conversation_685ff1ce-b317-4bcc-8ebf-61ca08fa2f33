// components/Tabs.jsx
import React from "react";
import styled from "styled-components";

const TabContainer = styled.div`
  display: flex;
  gap: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const TabButton = styled.button`
  all: unset;
  cursor: pointer;
  
  padding: 0.5rem 0.25rem;
  margin-bottom: -1px;
  color: ${(props) => (props.active ? "#059669" : "#6b7280")};
  border-bottom: ${(props) => (props.active ? "2px solid #059669" : "none")};
  font-weight: ${(props) => (props.active ? "500" : "400")};

  &:hover {
    color: ${(props) => (props.active ? "#059669" : "#374151")};
  }
`;

const Tabs = ({ tabs, activeTab, onTabChange }) => {
  return (
    <TabContainer>
      {tabs.map((tab) => (
        <TabButton
          key={tab}
          active={activeTab === tab}
          onClick={() => onTabChange(tab)}
        >
          {tab}
        </TabButton>
      ))}
    </TabContainer>
  );
};

export default Tabs;
