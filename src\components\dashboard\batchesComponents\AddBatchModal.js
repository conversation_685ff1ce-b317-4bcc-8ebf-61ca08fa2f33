import React, { useState } from "react";
import styled from "styled-components";
import { X, User, Calendar, Check } from "lucide-react";
import { mutations } from "@/api/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 500px;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const CloseButton = styled.button`
  background: #fde047;
  border: none;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
`;

const Required = styled.span`
  color: #ef4444;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 1rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
`;

const Button = styled.button`
  flex: 1;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;

  ${(props) =>
    props.$primary
      ? `
    background: #059669;
    color: white;
    border: none;
    &:hover {
      background: #047857;
    }
  `
      : `
    background: white;
    border: 1px solid #e5e7eb;
    &:hover {
      background: #f9fafb;
    }
  `}
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 1rem;
  height: auto;
  min-height: 42px;
`;

const Option = styled.option`
  padding: 0.5rem;
`;

const SelectContainer = styled.div`
  position: relative;
  width: 100%;
`;

const SelectButton = styled.button`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 1rem;
  background: white;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const DropdownList = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  margin-top: 0.25rem;
  max-height: 200px;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
`;

const DropdownItem = styled.div`
  padding: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    background: #f9fafb;
  }
`;

const SelectedDisplay = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
`;

const SelectedTag = styled.span`
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
`;

const AddBatchModal = ({ isOpen, onClose }) => {
  const queryClient = useQueryClient();
  const [batchData, setBatchData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    allowedInstitutions: [],
  });
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const institutionOptions = [
    "Private & Public University",
    "College of Education",
    "Nurses Training College",
  ];

  const { mutate: createBatch, isPending: createBatchLoading } = useMutation({
    ...mutations.createBatch,
    onSuccess: () => {
      queryClient.invalidateQueries(["batches"]);

      onClose();
      setBatchData({
        name: "",
        description: "",
        startDate: "",
        endDate: "",
        allowedInstitutions: [],
      });
    },
  });

  const handleInstitutionChange = (e) => {
    const selectedOptions = Array.from(
      e.target.selectedOptions,
      (option) => option.value
    );
    setBatchData({ ...batchData, allowedInstitutions: selectedOptions });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    createBatch(batchData);
  };

  const toggleInstitution = (institution) => {
    setBatchData((prev) => {
      if (prev.allowedInstitutions.includes(institution)) {
        return {
          ...prev,
          allowedInstitutions: prev.allowedInstitutions.filter(
            (i) => i !== institution
          ),
        };
      } else {
        return {
          ...prev,
          allowedInstitutions: [...prev.allowedInstitutions, institution],
        };
      }
    });
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalHeader>
          <Title>
            <User size={24} />
            Add New Batch
          </Title>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>
              Batch Names<Required>*</Required>
            </Label>
            <Input
              type="text"
              value={batchData.name}
              onChange={(e) =>
                setBatchData({ ...batchData, name: e.target.value })
              }
              placeholder="Batch 2025/2026"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>
              Description <Required>*</Required>
            </Label>
            <Input
              type="text"
              value={batchData.description}
              onChange={(e) =>
                setBatchData({ ...batchData, description: e.target.value })
              }
              placeholder="Enter description"
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>
              Allowed Institutions <Required>*</Required>
            </Label>
            <SelectContainer>
              <SelectButton
                type="button"
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                {batchData.allowedInstitutions.length > 0 ? (
                  <SelectedDisplay>
                    {batchData.allowedInstitutions.length <= 2
                      ? batchData.allowedInstitutions.map((inst) => (
                          <SelectedTag key={inst}>{inst}</SelectedTag>
                        ))
                      : `${batchData.allowedInstitutions.length} institutions selected`}
                  </SelectedDisplay>
                ) : (
                  "Select institutions......"
                )}
              </SelectButton>

              {dropdownOpen && (
                <DropdownList>
                  {institutionOptions.map((institution) => (
                    <DropdownItem
                      key={institution}
                      onClick={() => toggleInstitution(institution)}
                    >
                      {institution}
                      {batchData.allowedInstitutions.includes(institution) && (
                        <Check size={16} color="#059669" />
                      )}
                    </DropdownItem>
                  ))}
                </DropdownList>
              )}
            </SelectContainer>
          </FormGroup>

          <FormGroup>
            <Label>
              Start Date <Required>*</Required>
            </Label>
            <Input
              type="date"
              value={batchData.startDate}
              onChange={(e) =>
                setBatchData({ ...batchData, startDate: e.target.value })
              }
              required
            />
          </FormGroup>

          <FormGroup>
            <Label>
              End Date <Required>*</Required>
            </Label>
            <Input
              type="date"
              value={batchData.endDate}
              onChange={(e) =>
                setBatchData({ ...batchData, endDate: e.target.value })
              }
              required
            />
          </FormGroup>

          <ButtonGroup>
            <Button type="button" onClick={onClose}>
              Dismiss
            </Button>
            <Button type="submit" $primary disabled={createBatchLoading}>
              {createBatchLoading ? "Saving..." : "Save Changes"}
            </Button>
          </ButtonGroup>
        </form>
      </ModalContent>
    </ModalOverlay>
  );
};

export default AddBatchModal;
