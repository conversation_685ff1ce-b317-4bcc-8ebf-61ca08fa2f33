"use client";
import styled from "styled-components";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ff, <PERSON><PERSON>heckCircle, FiXCircle } from "react-icons/fi";
import React, { useState } from "react";
import { Loader2 } from "lucide-react";

const MaxWidthContainer = styled.div`
  width: 100%;
  max-width: 28rem;
  padding: 2rem;
  border: 1px solid #d1d5db;
  background: #fff;
  border-radius: 15px;
`;

const TextCenter = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const Subtitle = styled.p`
  color: #6b7280;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid #d1d5db;
  padding-bottom: 1rem;
`;

const UserIconContainer = styled.img`
  width: 80px;
  object-fit: cover;
`;

const FormGroup = styled.div`
  position: relative;
`;

const Label = styled.label`
  display: block;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
`;

const InputWrapper = styled.div`
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: white;
  margin-bottom: 20px;

  &:focus-within {
    border-color: ${(props) => (props.error ? "#dc2626" : "#006600")};
  }
`;

const Required = styled.span`
  color: #ef4444;
`;
const Icon = styled.span`
  position: absolute;
  left: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  font-size: 15px;
`;

const EyeIcon = styled.span`
  position: absolute;
  right: 12px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 18px;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: #374151;
  }
`;

const Input = styled.input`
  width: 95%;
  padding: 12px 10px 12px 35px; // Added left padding for icon
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #006600;
    box-shadow: 0 0 0 1px #006600;
  }
`;

const ValidationList = styled.ul`
  list-style: none;
  padding: 0;
  margin-bottom: 1rem;
`;

const ValidationItem = styled.li`
  display: flex;
  align-items: center;
  color: ${({ $valid }) => ($valid ? "green" : "gray")};
  font-size: 14px;
  gap: 0.5rem;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Button = styled.button`
  width: 100%;
  background-color: #16a34a;
  color: white;
  padding: 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  transition: background-color 0.2s;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  opacity: ${props => props.disabled ? 0.7 : 1};
  
  &:hover {
    background-color: ${props => props.disabled ? '#16a34a' : '#15803d'};
  }
`;

// const Spinner = styled(FiLoader)`
//   animation: spin 1s linear infinite;
  
//   @keyframes spin {
//     from {
//       transform: rotate(0deg);
//     }
//     to {
//       transform: rotate(360deg);
//     }
//   }
//   &:disabled {
//     opacity: 0.5;
//     cursor: not-allowed;
//   }
// `;

// Add these styled components after your existing styled components
const CheckboxContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
`;

const Checkbox = styled.input`
  cursor: pointer;
  width: 16px;
  height: 16px;
`;

const Link = styled.a`
  color: #16a34a;
  text-decoration: none;
  font-size: 0.875rem;

  &:hover {
    text-decoration: underline;
  }
`;

const StyledButton = styled.button`
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: ${({ $outline }) => ($outline ? "1px solid #F6EB16" : "none")};
  background-color: ${({ $outline, $color }) => {
    if ($outline) return "transparent";
    if ($color) return $color;
    return "#119411";
  }};
  color: ${({ $outline }) => ($outline ? "#333" : "white")};
  font-weight: bold;
  cursor: pointer;
  margin-top: ${({ $marginTop }) => $marginTop || "0"};
`;

const AnimatedLoader = styled(Loader2)`
  height: fit-content;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

const AuthForm = ({
  title,
  subtitle,
  fields = [], // Add default empty array
  buttonText,
  onSubmit,
  isResetPassword,
  links,
  titleIcon, // Add this prop
  buttons = [],
  pending,
  disabled, // Add default value for buttons
}) => {
  const [formData, setFormData] = useState(() => {
    const initialState = {};

    // Handle array of fields
    if (Array.isArray(fields)) {
      fields.forEach((field) => {
        initialState[field.name] = "";
      });
    }

    return initialState;
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const password = formData.password || "";
  const confirmPassword = formData.confirmPassword || "";
  const doPasswordsMatch =
    password && confirmPassword && password === confirmPassword;

  const isUppercase = /[A-Z]/.test(password);
  const isNumber = /[0-9]/.test(password);
  const isLengthValid = password.length >= 5;
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <MaxWidthContainer>
      <TextCenter>
        {titleIcon ? (
          <UserIconContainer src={titleIcon} alt="Title Icon" />
        ) : (
          <svg
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#9ca3af"
            strokeWidth="2"
          >
            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        )}

        <Title>{title}</Title>
        <Subtitle>{subtitle}</Subtitle>
      </TextCenter>

      {Array.isArray(fields) && fields.length > 0 && (
        <Form onSubmit={handleSubmit}>
          {fields.map((field) => {
            if (field.type === "checkbox") {
              return (
                <CheckboxContainer key={field.name}>
                  <CheckboxLabel>
                    <Checkbox
                      type="checkbox"
                      name={field.name}
                      checked={formData[field.name]}
                      onChange={handleChange}
                    />
                    {field.label}
                  </CheckboxLabel>
                  {field.name === "keepLoggedIn" && links?.forgotPassword && (
                    <Link href={links.forgotPassword}>Forgot Password?</Link>
                  )}
                </CheckboxContainer>
              );
            }

            return (
              <FormGroup key={field.name}>
                <Label>
                  {field.label} {field.required && <Required>*</Required>}
                </Label>
                <InputWrapper>
                  <Icon>{field.icon}</Icon>
                  <Input
                    type={
                      field.type === "password"
                        ? field.name === "confirmPassword"
                          ? showConfirmPassword
                            ? "text"
                            : "password"
                          : showPassword
                          ? "text"
                          : "password"
                        : field.type
                    }
                    name={field.name}
                    placeholder={field.placeholder}
                    value={formData[field.name]}
                    onChange={handleChange}
                    required={field.required}
                  />
                  {field.type === "password" && (
                    <EyeIcon
                      onClick={() =>
                        field.name === "confirmPassword"
                          ? setShowConfirmPassword(!showConfirmPassword)
                          : setShowPassword(!showPassword)
                      }
                    >
                      {field.name === "confirmPassword" ? (
                        showConfirmPassword ? (
                          <FiEyeOff />
                        ) : (
                          <FiEye />
                        )
                      ) : showPassword ? (
                        <FiEyeOff />
                      ) : (
                        <FiEye />
                      )}
                    </EyeIcon>
                  )}
                </InputWrapper>

                {isResetPassword && field.name === "password" && (
                  <ValidationList>
                    <ValidationItem $valid={isUppercase}>
                      {isUppercase ? (
                        <FiCheckCircle color="green" />
                      ) : (
                        <FiXCircle color="gray" />
                      )}
                      At least 1 uppercase letter
                    </ValidationItem>
                    <ValidationItem $valid={isNumber}>
                      {isNumber ? (
                        <FiCheckCircle color="green" />
                      ) : (
                        <FiXCircle color="gray" />
                      )}
                      At least 1 number
                    </ValidationItem>
                    <ValidationItem $valid={isLengthValid}>
                      {isLengthValid ? (
                        <FiCheckCircle color="green" />
                      ) : (
                        <FiXCircle color="gray" />
                      )}
                      At least 5 characters
                    </ValidationItem>
                  </ValidationList>
                )}

                {field.name === "confirmPassword" && (
                  <ValidationList>
                    <ValidationItem $valid={doPasswordsMatch}>
                      {doPasswordsMatch ? (
                        <FiCheckCircle color="green" />
                      ) : (
                        <FiXCircle color="gray" />
                      )}
                      Passwords must match
                    </ValidationItem>
                  </ValidationList>
                )}
              </FormGroup>
            );
          })}
          <Button type="submit" disabled={pending || disabled}>
            {pending ? (
              <AnimatedLoader className="animate-spin" size={16} />
            ) : (
              buttonText
            )}
          </Button>
        </Form>
      )}

      {buttons.length > 0 && (
        <div style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
          {buttons.map((button, index) => (
            <StyledButton
              key={index}
              onClick={button.onClick}
              $outline={index === 1}
            >
              {button.text}
            </StyledButton>
          ))}
        </div>
      )}
    </MaxWidthContainer>
  );
};

export default AuthForm;
