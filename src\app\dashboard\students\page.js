"use client";
import React, { useState, useEffect, useMemo } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { queries } from "@/api/client";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { FileText, InboxIcon, MoreVertical } from "lucide-react";
import { DataTable } from "@globalicons/enterprise-tools";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";

// Styled components matching the students page design
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1.75rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => {
    if (!props.$active) return "#6b7280";
    return props.$type === "archived" ? "#ef4444" : "#16a34a";
  }};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => {
      if (!props.$active) return "transparent";
      return props.$type === "archived" ? "#ef4444" : "#16a34a";
    }};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: ${(props) => (props.$type === "archived" ? "#ef4444" : "#16a34a")};
  }
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

const StudentsTable = () => {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [activeFilter, setActiveFilter] = useState("active");
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // Enhanced auth check
  useEffect(() => {
    const token = sessionStorage.getItem("authToken");
    if (!token) {
      push("/login");
    } else {
      queryClient.invalidateQueries(["students"]);
    }
  }, [push, queryClient]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  // Fetch all students data with modified query
  const {
    data: studentsData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listStudents,
    queryFn: () => queries.listStudents.queryFn({ status: activeFilter }),
    retry: 1,
    onError: (error) => {
      console.error("API Error:", error);
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Add the filteredStudents memo
  const filteredStudents = useMemo(() => {
    if (!studentsData?.data) return [];

    switch (activeFilter) {
      case "active":
        // Show all non-archived students regardless of enrollment status
        return studentsData.data.filter((student) => !student.archived);
      case "archived":
        return studentsData.data.filter((student) => student.archived === true);
      case "deferred": // Changed from "deferments" to "deferred" to match your button
        const deferredStudents = studentsData.data.filter(
          (student) => student.enrollmentStatus?.toLowerCase() === "deferred"
        );
        return deferredStudents.length > 0 ? deferredStudents : null; // Return null if empty to show EmptyState
      default:
        return studentsData.data;
    }
  }, [studentsData?.data, activeFilter]);

  // Update the DataTable to use filteredStudents
  const handleArchive = (student) => {
    // Add your archive logic here
    console.log("Archiving student:", student);
  };

  // Define columns configuration
  const columns = [
    {
      id: "passportPhoto",
      accessorKey: "passportPhoto",
      header: "Photo",
      size: 80,
      cell: (info) => {
        const photoUrl = info.getValue();
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "40px",
              height: "40px",
              borderRadius: "50%",
              overflow: "hidden",
              backgroundColor: "#f3f4f6",
            }}
          >
            <img
              src={photoUrl || "/profile-pic-placeholder.svg"}
              alt="Student Photo"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </div>
        );
      },
    },
    {
      id: "studentId",
      accessorKey: "studentId",
      header: "Student ID",
    },
    {
      id: "firstName",
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      id: "middleName",
      accessorKey: "middleName",
      header: "Middle Name",
    },
    {
      id: "lastName",
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      id: "institution",
      accessorKey: "institution",
      header: "Institution",
      filterType: "select",
    },
    {
      id: "batch",
      accessorKey: "batch",
      header: "Batch",
      filterType: "select",
    },
    {
      id: "course",
      accessorKey: "course",
      header: "Course",
      filterType: "select",
      cell: (info) => {
        const courseData = info.getValue();
        return courseData?.name || "N/A";
      },
    },
    {
      id: "phoneNumber",
      accessorKey: "phoneNumber",
      header: "Phone Number",
      size: 150,
    },
    {
      id: "email",
      accessorKey: "emailAddress",
      header: "Email",
    },
    {
      id: "companyPostedTo",
      accessorKey: "posting",
      header: "Company Posted To",
      size: 150,
      filterType: "select",
      cell: (info) => {
        const posting = info.getValue();
        return posting.company || "Not Assigned";
      },
    },
    {
      id: "regionPostedTo",
      accessorKey: "posting",
      header: "Region Posted To",
      size: 150,
      filterType: "select",
      cell: (info) => {
        const posting = info.getValue();
        return posting.region || "Not Assigned";
      },
    },
    {
      id: "nssNumber",
      accessorKey: "nssNumber",
      header: "NSS Number",
    },
    {
      id: "enrollmentStatus",
      accessorKey: "enrollmentStatus",
      header: "Enrollment Status",
      filterType: "select",
      cell: (info) => {
        const enrollmentStatus = info.getValue();
        if (!enrollmentStatus) return "N/A";

        let color;
        // Convert to lowercase for case-insensitive comparison
        const status = enrollmentStatus.toLowerCase();

        switch (status) {
          case "awaiting posting":
            color = "#16a34a"; // green
            break;
          case "awaiting registration":
            color = "#f59e0b"; // amber
            break;
          case "details submitted":
            color = "#6b7280"; // gray
            break;
          case "deferred":
            color = "#dc2626"; // red-600 for deferred status
            break;
          default:
            color = "#3b82f6"; // blue for other statuses
        }

        // For debugging
        console.log("Enrollment Status:", enrollmentStatus, "Color:", color);

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {enrollmentStatus.charAt(0).toUpperCase() +
              enrollmentStatus.slice(1).toLowerCase()}
          </span>
        );
      },
    },
    {
      id: "dateRegistered",
      accessorKey: "dateRegistered",
      header: "Date Registered",
      filterType: "date-range",
      cell: (info) => {
        const dateValue = info.getValue();
        return dateValue
          ? new Date(dateValue).toISOString().split("T")[0]
          : "N/A";
      },
    },
    // Add this state at the top of the StudentsTable component

    // Update the actions column definition
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left by 100px
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  // minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    const studentData = info.row.original;
                    setSelectedStudent(studentData);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleArchive(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                    color: "#ef4444",
                  }}
                >
                  Archive
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  // Add this CSS to your component
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .dropdown {
        position: relative;
        display: inline-block;
      }
      .dropdown:hover .dropdown-content {
        display: block;
      }
      .dropdown-content button:hover {
        background-color: #f3f4f6;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
    // Invalidate and refetch with new filter
    queryClient.invalidateQueries(["students"]);
  };

  return (
    <Container>
      <TableContainer>
        <TableHeader>
          <TitleWrapper>
            <FileText size={20} color="#6b7280" />
            <TableTitle>Uploaded Personnel</TableTitle>
          </TitleWrapper>
          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "active"}
              $type="active"
              onClick={() => handleFilterChange("active")}
            >
              All
            </FilterButton>
            <FilterButton
              $active={activeFilter === "archived"}
              $type="archived"
              onClick={() => handleFilterChange("archived")}
            >
              Archived
            </FilterButton>
            <FilterButton
              $active={activeFilter === "deferred"}
              $type="deferred"
              onClick={() => handleFilterChange("deferred")}
            >
              Deferments
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading students...</LoaderText>
            </LoaderContainer>
          ) : error ? (
            <div
              style={{
                padding: "2rem",
                textAlign: "center",
                color: "#ef4444",
              }}
            >
              Error loading personnel: {error.message}
            </div>
          ) : activeFilter === "archived" ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No archived records found</EmptyStateText>
            </EmptyState>
          ) : activeFilter === "deferred" && (!filteredStudents || filteredStudents.length === 0) ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No deferment applications found</EmptyStateText>
            </EmptyState>
          ) : studentsData?.data && studentsData.data.length > 0 ? (
            <DataTable
              data={filteredStudents || []} // Use filteredStudents instead of studentsData.data
              columns={columns}
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
              onRowClick={(row) => {
                setOpenPeakDrawer(false);
                setSelectedStudent(row);
                setOpenPeakDrawer(true);
              }}
            />
          ) : (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No personnel records found</EmptyStateText>
            </EmptyState>
          )}
        </TableContent>
      </TableContainer>

      {/* PeakDetails component */}
      <PeakDetails
        student={selectedStudent}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />
    </Container>
  );
};

export default StudentsTable;
