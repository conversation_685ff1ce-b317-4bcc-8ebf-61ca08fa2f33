"use client";

import ActionButton from "@/components/dashboard/batchesComponents/ActionButton";
import BatchCard from "@/components/dashboard/batchesComponents/BatchesCard";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import styled from "styled-components";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { queries } from "@/api/client";
import { FileText, InboxIcon } from "lucide-react";
import SearchBar from "@/components/dashboard/batchesComponents/SearchBar";
import Pagination from "@/components/dashboard/batchesComponents/Pagination";

// Styled components matching the students page design
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1.75rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => {
    if (!props.$active) return "#6b7280";
    return props.$type === "archived" ? "#ef4444" : "#16a34a";
  }};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => {
      if (!props.$active) return "transparent";
      return props.$type === "archived" ? "#ef4444" : "#16a34a";
    }};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: ${(props) => (props.$type === "archived" ? "#ef4444" : "#16a34a")};
  }
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem;
  padding: 1rem;

  @media (min-width: 768px) {
    grid-template-columns: ${(props) =>
      props.isEmpty ? "1fr" : "repeat(2, 1fr)"};
  }

  @media (min-width: 1024px) {
    grid-template-columns: ${(props) =>
      props.isEmpty ? "1fr" : "repeat(3, 1fr)"};
  }
`;

// Add this styled component
const HeaderActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const ActionContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

// Update the TableHeader section

const AllBatches = () => {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [activeFilter, setActiveFilter] = useState("active");

  // Enhanced auth check
  React.useEffect(() => {
    const token = sessionStorage.getItem("authToken");
    if (!token) {
      push("/login");
    } else {
      queryClient.invalidateQueries(["batches"]);
    }
  }, [push, queryClient]);

  // State declarations
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);

  // Query declaration
  const {
    data: batchesData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listBatches,
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  console.log("batchesData", batchesData);

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const filteredBatches = React.useMemo(() => {
    if (!batchesData?.data) return [];
    return batchesData.data.filter((batch) =>
      batch.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [batchesData?.data, searchQuery]);

  return (
    <Container>
      <TableContainer>
        <TableHeader>
          <HeaderActions>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />
              <TableTitle>Uploaded Enrollment</TableTitle>
            </TitleWrapper>
            <ActionContainer>
              <SearchBar value={searchQuery} onChange={handleSearchChange} />
              <ActionButton label="Add New Enrollment" />
            </ActionContainer>
          </HeaderActions>
          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "active"}
              $type="active"
              onClick={() => handleFilterChange("active")}
            >
              Active
            </FilterButton>
            <FilterButton
              $active={activeFilter === "archived"}
              $type="archived"
              onClick={() => handleFilterChange("archived")}
            >
              Archived
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading enrollment...</LoaderText>
            </LoaderContainer>
          ) : activeFilter === "archived" ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No archived enrollment found</EmptyStateText>
            </EmptyState>
          ) : (
            <>
              <Grid isEmpty={filteredBatches.length === 0}>
                {filteredBatches.length > 0 ? (
                  filteredBatches.map((batch) => (
                    <BatchCard
                      key={batch._id}
                      id={batch._id}
                      title={batch.name}
                      created={batch.startDate}
                      startDate={batch.startDate}
                      endDate={batch.endDate}
                      stage={batch.stage}
                      institutions={batch.allowedInstitutions}
                      onClick={() => push(`/dashboard/batches/${batch._id}`)}
                    />
                  ))
                ) : (
                  <EmptyState>
                    <EmptyStateIcon>
                      <InboxIcon size={64} color="#9ca3af" />
                    </EmptyStateIcon>
                    <EmptyStateText>No Enrollment found</EmptyStateText>
                  </EmptyState>
                )}
              </Grid>
              <Pagination
                currentPage={currentPage}
                totalItems={filteredBatches.length || 0}
                itemsPerPage={itemsPerPage}
                onPageChange={(page) => setCurrentPage(page)}
              />
            </>
          )}
        </TableContent>
      </TableContainer>
    </Container>
  );
};

export default AllBatches;
