"use client";
import React, { useState, useEffect } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { queries, mutations, api } from "@/api/client";
import { useRouter } from "next/navigation";
import { FileText, InboxIcon, MoreVertical, X } from "lucide-react";
import { DataTable } from "@globalicons/enterprise-tools";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";
import toast from "react-hot-toast";
// Import styled components from the new file
import {
  Container,
  PageTitle,
  TableContainer,
  TitleWrapper,
  TableHeader,
  TableTitle,
  ButtonGroup,
  FilterButton,
  TableContent,
  LoaderContainer,
  LoaderSpinner,
  LoaderText,
  EmptyState,
  EmptyStateIcon,
  EmptyStateText,
  PageHeader,
  CreateButton,
  ModalOverlay,
  ModalContainer,
  ModalHeader,
  ModalTitle,
  CloseButton,
  ModalContent,
  FormGroup,
  FormLabel,
  FormInput,
  FormSelect,
  Modal<PERSON>ooter,
  CancelButton,
  SubmitButton,
} from "@/components/styles/dashboard/companies.styled";

const CompaniesTable = () => {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [activeFilter, setActiveFilter] = useState("active");
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newAgency, setNewAgency] = useState({
    name: "",
    email: "",
    address: "",
    digitalAddress: "",
    registrationNumber: "",
    industry: "",
    contact: "",
    companyType: "Government",
    approvalStatus: "Pending",
    TINNumber: "",
    region: "",
  });
  const [uploadedFiles, setUploadedFiles] = useState({
    taxCertificate: null,
    registrationCertificate: null,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Enhanced auth check
  useEffect(() => {
    const token = sessionStorage.getItem("authToken");
    if (!token) {
      push("/login");
    } else {
      queryClient.invalidateQueries(["companies"]); // Update to use companies query key
    }
  }, [push, queryClient]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [openDropdownId]);

  // Fetch all students data with modified query
  // Update the useQuery to use the appropriate query based on the activeFilter
  const {
    data: companiesData,
    isLoading,
    error,
  } = useQuery({
    ...(activeFilter === "pending"
      ? queries.listPendingCompanies
      : activeFilter === "rejected"
      ? queries.listArchivedCompanies
      : queries.listCompanies),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // console.log("Companies Data:", companiesData);

  // Define columns configuration
  const columns = [
    {
      id: "CompanyName",
      accessorKey: "name",
      header: "Company Name",
    },
    {
      id: "CompanyEmail",
      accessorKey: "email",
      header: "Company Email",
    },
    {
      id: "CompanyAddress",
      accessorKey: "address",
      header: "Company Address",
      size: 150,
    },
    {
      id: "industry",
      accessorKey: "industry",
      header: "Industry",
      size: 150,
    },

    {
      id: "phoneNumber",
      accessorKey: "contact",
      header: "Company Phone Number",
      size: 150,
    },

    {
      id: "registrationCertificate",
      accessorKey: "registrationCertificate",
      header: "Registration Certificate",
      type: "pdf",
      cell: (info) => {
        const docPath = info.getValue();
        if (!docPath) return "—";

        return (
          <a
            href={docPath}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              color: "#2563eb",
              textDecoration: "none",
              display: "flex",
              alignItems: "center",
              gap: "4px",
            }}
            onClick={(e) => {
              e.stopPropagation(); // Prevent row selection when clicking the link
            }}
          >
            Registration Document
          </a>
        );
      },
    },
    {
      id: "taxCertificate",
      accessorKey: "taxCertificate",
      header: "Tax Certificate",
      type: "pdf",
      cell: (info) => {
        const docPath = info.getValue();
        if (!docPath) return "—";

        return (
          <a
            href={docPath}
            target="_blank"
            rel="noopener noreferrer"
            style={{
              color: "#2563eb",
              textDecoration: "none",
              display: "flex",
              alignItems: "center",
              gap: "4px",
            }}
            onClick={(e) => {
              e.stopPropagation(); // Prevent row selection when clicking the link
            }}
          >
            Tax Document
          </a>
        );
      },
    },

    {
      id: "companyType",
      accessorKey: "companyType",
      header: "Company Type",
    },
    {
      id: "Status",
      accessorKey: "approvalStatus",
      header: "Status",
      filterType: "select",
      cell: (info) => {
        const status = info.getValue();
        let color;

        switch (status.toLowerCase()) {
          case "approved":
            color = "#16a34a"; // green
            break;
          case "rejected":
            color = "#ef4444"; // red
            break;
          case "archived":
            color = "#6b7280"; // gray
            break;
          default:
            color = "#f59e0b"; // amber for pending
        }

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
          </span>
        );
      },
    },
    {
      id: "dateRegistered",
      accessorKey: "createdAt",
      header: "Date Registered",
      filterType: "date-range",
      cell: (info) => {
        const dateValue = info.getValue();
        if (!dateValue) return "—";

        // Parse the date and format it as DD-MM-YYYY
        const date = new Date(dateValue);
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear();

        return `${day}-${month}-${year}`;
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left by 100px
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  // minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedCompany(info.row.original);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleArchive(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                    color: "#ef4444",
                  }}
                >
                  Archive
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
    // Invalidate the query when changing filters with the correct query key
    if (filter === "pending") {
      // Make sure this matches the actual query key used in queries.listPendingCompanies
      queryClient.invalidateQueries(["companies", "pending"]);
    } else if (filter === "archived") {
      queryClient.invalidateQueries(["companies", "rejected"]);
    } else {
      queryClient.invalidateQueries(["companies"]);
    }
  };

  // Add function to handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    console.log(`Updating ${name} to: ${value}`); // Add this for debugging
    setNewAgency((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setUploadedFiles((prev) => ({
        ...prev,
        [name]: {
          file: files[0],
          fileName: files[0].name,
          fileType: files[0].type,
        },
      }));
    }
  };

  // Add the mutation hook
  const createCompanyMutation = useMutation(mutations.createPowerUserCompany);
  const uploadFileMutation = useMutation(mutations.uploadFile);

  // Add these queries for presigned URLs
  const {
    refetch: fetchTaxCertificatePresignedUrl,
    isLoading: isFetchingTaxCertificatePresignedUrl,
  } = useQuery({
    queryKey: ["getPresignedUrl", "taxCertificate"],
    queryFn: () =>
      api.getPresignedUrl({
        companyName: newAgency.email,
        fileName: uploadedFiles.taxCertificate?.fileName,
        fileType: uploadedFiles.taxCertificate?.fileType,
        uploadType: "taxCertificate",
      }),
    enabled: false,
  });

  const {
    refetch: fetchRegistrationCertificatePresignedUrl,
    isLoading: isFetchingRegistrationCertificatePresignedUrl,
  } = useQuery({
    queryKey: ["getPresignedUrl", "registrationCertificate"],
    queryFn: () =>
      api.getPresignedUrl({
        companyName: newAgency.email,
        fileName: uploadedFiles.registrationCertificate?.fileName,
        fileType: uploadedFiles.registrationCertificate?.fileType,
        uploadType: "registrationCertificate",
      }),
    enabled: false,
  });

  const stripUrlParams = (url) => {
    try {
      return url.split("?")[0];
    } catch (error) {
      console.error("Error stripping URL parameters:", error);
      return url;
    }
  };

  // Add function to handle form submission
  const handleCreateAgency = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setIsUploading(true);

    try {
      let taxCertificateUrl = "";
      let registrationCertificateUrl = "";

      // Only process file uploads if company type is Private and files are selected
      if (newAgency.companyType === "Private") {
        const [taxCertRes, regCertRes] = await Promise.all([
          fetchTaxCertificatePresignedUrl(),
          fetchRegistrationCertificatePresignedUrl(),
        ]);

        console.log("URLS", taxCertRes.data, regCertRes.data);
        console.log(
          "FILES INFO",
          uploadedFiles.taxCertificate.file,
          uploadedFiles.registrationCertificate.file
        );

        try {
          await uploadFileMutation.mutate({
            url: taxCertRes.data,
            file: uploadedFiles.taxCertificate.file,
          });
          await uploadFileMutation.mutate({
            url: regCertRes.data,
            file: uploadedFiles.registrationCertificate.file,
          });

          taxCertificateUrl = stripUrlParams(taxCertRes.data);
          registrationCertificateUrl = stripUrlParams(regCertRes.data);
        } catch (e) {
          toast.error("Error uploading files. Please try again.");
          return;
        }
      }

      // Create company with file URLs
      createCompanyMutation.mutate({
        ...newAgency,
        companyName: newAgency.name,
        ...(newAgency.companyType === "Private" && {
          taxCertificate: taxCertificateUrl,
          registrationCertificate: registrationCertificateUrl,
        }),
      });

      // Close modal and show success message
      setShowCreateModal(false);

      // Reset form and refresh data
      setNewAgency({
        name: "",
        email: "",
        address: "",
        digitalAddress: "",
        registrationNumber: "",
        industry: "",
        contact: "",
        companyType: "Government",
        TINNumber: "",
        region: "",
        taxCertificate: "",
        registrationCertificate: "",
      });

      setUploadedFiles({
        taxCertificate: null,
        registrationCertificate: null,
      });

      // Refresh the companies list
      queryClient.invalidateQueries(["companies"]);
    } catch (error) {
      alert("Failed to create user agency. Please try again.");
      console.error("Creation error:", error);
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
    }
  };

  return (
    <Container>
      <TableContainer>
        <TableHeader>
          <PageHeader>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />
              <TableTitle>Uploaded Entries</TableTitle>
            </TitleWrapper>
            <CreateButton onClick={() => setShowCreateModal(true)}>
              Create New Agency
            </CreateButton>
          </PageHeader>

          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "active"}
              $type="active"
              onClick={() => handleFilterChange("active")}
            >
              Active User Agencies
            </FilterButton>
            <FilterButton
              $active={activeFilter === "pending"}
              $type="activ"
              onClick={() => handleFilterChange("pending")}
            >
              User Agencies Pending Approval
            </FilterButton>
            <FilterButton
              $active={activeFilter === "rejected"}
              $type="archived"
              onClick={() => handleFilterChange("rejected")}
            >
              Rejected User Agencies
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading user agencies ...</LoaderText>
            </LoaderContainer>
          ) : error ? (
            <div
              style={{
                padding: "2rem",
                textAlign: "center",
                color: "#ef4444",
              }}
            >
              Error loading user agencies : {error.message}
            </div>
          ) : activeFilter === "rejected" &&
            !companiesData?.some(
              (company) => company?.approvalStatus.toLowerCase() === "rejected"
            ) ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No archived user agencies found</EmptyStateText>
            </EmptyState>
          ) : activeFilter === "pending" &&
            !companiesData?.some(
              (company) => company?.approvalStatus.toLowerCase() === "pending"
            ) ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>
                No pending user agency approval found
              </EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              data={
                activeFilter === "rejected"
                  ? companiesData?.filter(
                      (company) =>
                        company?.approvalStatus.toLowerCase() === "rejected"
                    ) || []
                  : activeFilter === "pending"
                  ? companiesData?.filter(
                      (company) =>
                        company?.approvalStatus.toLowerCase() === "pending"
                    ) || []
                  : companiesData?.filter(
                      (company) =>
                        company?.approvalStatus.toLowerCase() !== "rejected"
                    ) || []
              }
              columns={columns}
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
              onRowClick={(row) => {
                setOpenPeakDrawer(false);
                setSelectedCompany(row);
                setOpenPeakDrawer(true);
              }}
            />
          )}
        </TableContent>
      </TableContainer>

      {/* PeakDetails component */}
      <PeakDetails
        company={selectedCompany}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />

      {/* Create Agency Modal */}
      {showCreateModal && (
        <ModalOverlay>
          <ModalContainer>
            <ModalHeader>
              <ModalTitle>Create New User Agency</ModalTitle>
              <CloseButton onClick={() => setShowCreateModal(false)}>
                <X size={20} />
              </CloseButton>
            </ModalHeader>

            <form onSubmit={handleCreateAgency}>
              <ModalContent>
                <FormGroup>
                  <FormLabel>Company Type*</FormLabel>
                  <FormSelect
                    name="companyType"
                    value={newAgency.companyType}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Government">Government</option>
                    <option value="Private">Private</option>
                  </FormSelect>
                </FormGroup>

                <FormGroup>
                  <FormLabel>Agency Name*</FormLabel>
                  <FormInput
                    type="text"
                    name="name"
                    value={newAgency.name}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Email Address*</FormLabel>
                  <FormInput
                    type="email"
                    name="email"
                    value={newAgency.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Contact Number*</FormLabel>
                  <FormInput
                    type="tel"
                    name="contact"
                    value={newAgency.contact}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Company Address*</FormLabel>
                  <FormInput
                    type="text"
                    name="address"
                    value={newAgency.address}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Digital Address*</FormLabel>
                  <FormInput
                    type="text"
                    name="digitalAddress"
                    value={newAgency.digitalAddress || ""}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Registration Number*</FormLabel>
                  <FormInput
                    type="text"
                    name="registrationNumber"
                    value={newAgency.registrationNumber || ""}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <FormLabel>Region*</FormLabel>
                  <FormSelect
                    name="region"
                    value={newAgency.region}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select a region</option>
                    <option value="Greater Accra Region">
                      Greater Accra Region
                    </option>
                    <option value="Ashanti Region">Ashanti Region</option>
                    <option value="Western Region">Western Region</option>
                    <option value="Eastern Region">Eastern Region</option>
                    <option value="Central Region">Central Region</option>
                    <option value="Volta Region">Volta Region</option>
                    <option value="Northern Region">Northern Region</option>
                    <option value="Upper East Region">Upper East Region</option>
                    <option value="Upper West Region">Upper West Region</option>
                    <option value="North East Region">North East Region</option>
                    <option value="Savannah Region">Savannah Region</option>
                    <option value="Bono Region">Bono Region</option>
                    <option value="Bono East Region">Bono East Region</option>
                    <option value="Ahafo Region">Ahafo Region</option>
                    <option value="Western North Region">
                      Western North Region
                    </option>
                    <option value="Oti Region">Oti Region</option>
                  </FormSelect>
                </FormGroup>

                <FormGroup>
                  <FormLabel>Industry*</FormLabel>
                  <FormSelect
                    name="industry"
                    value={newAgency.industry}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select Industry</option>
                    <option value="Education">Education</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Telecom/ICT">Telecom/ICT</option>
                    <option value="Utilities">Utilities</option>
                    <option value="Insurance">Insurance</option>
                    <option value="Agriculture">Agriculture</option>
                    <option value="Manufacturing">Manufacturing</option>
                    <option value="Construction">Construction</option>
                    <option value="Transport/Aerospace">
                      Transport/Aerospace
                    </option>
                    <option value="Hospitality">Hospitality</option>
                    <option value="Oil & Gas">Oil & Gas</option>
                    <option value="Estate & Housing">Estate & Housing</option>
                    <option value="Banking & Finance">Banking & Finance</option>
                    <option value="Media">Media</option>
                    <option value="Food Industry">Food Industry</option>
                    <option value="Security Industry">Security Industry</option>
                    <option value="Shipping & Port">Shipping & Port</option>
                    <option value="Mining/Quarry">Mining/Quarry</option>
                    <option value="Securities/Brokers">
                      Securities/Brokers
                    </option>
                    <option value="Commerce/Trading">Commerce/Trading</option>
                    <option value="Refinery of Minerals">
                      Refinery of Minerals
                    </option>
                    <option value="Sand Winning">Sand Winning</option>
                    <option value="Legal">Legal</option>
                    <option value="Tourism">Tourism</option>
                  </FormSelect>
                </FormGroup>

                {newAgency.companyType === "Private" && (
                  <FormGroup>
                    <FormLabel>TIN Number*</FormLabel>
                    <FormInput
                      type="text"
                      name="TINNumber"
                      value={newAgency.TINNumber}
                      onChange={handleInputChange}
                      required={newAgency.companyType === "Private"}
                    />
                  </FormGroup>
                )}

                {newAgency.companyType === "Private" && (
                  <>
                    <FormGroup>
                      <FormLabel>Registration Certificate</FormLabel>
                      <FormInput
                        type="file"
                        name="registrationCertificate"
                        accept=".pdf"
                        required={newAgency.companyType === "Private"}
                        onChange={handleFileChange}
                      />
                      {uploadedFiles.registrationCertificate && (
                        <div
                          style={{
                            fontSize: "0.8rem",
                            marginTop: "0.25rem",
                            color: "#16a34a",
                          }}
                        >
                          File selected:{" "}
                          {uploadedFiles.registrationCertificate.fileName}
                        </div>
                      )}
                    </FormGroup>

                    <FormGroup>
                      <FormLabel>Tax Certificate</FormLabel>
                      <FormInput
                        type="file"
                        name="taxCertificate"
                        accept=".pdf"
                        required={newAgency.companyType === "Private"}
                        onChange={handleFileChange}
                      />
                      {uploadedFiles.taxCertificate && (
                        <div
                          style={{
                            fontSize: "0.8rem",
                            marginTop: "0.25rem",
                            color: "#16a34a",
                          }}
                        >
                          File selected: {uploadedFiles.taxCertificate.fileName}
                        </div>
                      )}
                    </FormGroup>
                  </>
                )}
              </ModalContent>

              <ModalFooter>
                <CancelButton
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancel
                </CancelButton>
                <SubmitButton type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Creating..." : "Create Agency"}
                </SubmitButton>
              </ModalFooter>
            </form>
          </ModalContainer>
        </ModalOverlay>
      )}
    </Container>
  );
};

export default CompaniesTable;
