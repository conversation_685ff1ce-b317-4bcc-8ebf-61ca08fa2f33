"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";
import React, { useState, useEffect } from "react";
import { Users, MoreVertical, InboxIcon } from "lucide-react";
import { DataTable } from "@globalicons/enterprise-tools";

export default function Posting() {
  const params = useParams();
  const { push } = useRouter();
  const companyId = params.companyId;
  const [activeFilter, setActiveFilter] = useState("regular");
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [selectedPersonnel, setSelectedPersonnel] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);

  const { data: companyData } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Mock data for personnel - replace with actual API call
  const [personnelData, setPersonnelData] = useState({
    regular: [],
    special: [],
    isLoading: true,
  });

  useEffect(() => {
    // Simulate API call
    const fetchPersonnel = async () => {
      try {
        // Replace with actual API call
        setTimeout(() => {
          setPersonnelData({
            regular: [
              {
                id: "1",
                firstName: "John",
                lastName: "Doe",
                nssNumber: "NSS12345",
                institution: "University of Ghana",
                course: "Computer Science",
                phoneNumber: "0241234567",
                email: "<EMAIL>",
                postingDate: "2023-09-01",
                status: "Active",
              },
              {
                id: "2",
                firstName: "Jane",
                lastName: "Smith",
                nssNumber: "NSS67890",
                institution: "KNUST",
                course: "Engineering",
                phoneNumber: "0257654321",
                email: "<EMAIL>",
                postingDate: "2023-09-01",
                status: "Active",
              },
            ],
            special: [
              {
                id: "3",
                firstName: "Michael",
                lastName: "Johnson",
                nssNumber: "NSS24680",
                institution: "UCC",
                course: "Medicine",
                phoneNumber: "**********",
                email: "<EMAIL>",
                postingDate: "2023-09-15",
                status: "Active",
                specialReason: "Medical Expertise",
              },
            ],
            isLoading: false,
          });
        }, 1000);
      } catch (error) {
        console.error("Error fetching personnel:", error);
        setPersonnelData((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchPersonnel();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  const company = companyData?.data;

  // Define columns configuration
  const columns = [
    {
      id: "nssNumber",
      accessorKey: "nssNumber",
      header: "NSS Number",
    },
    {
      id: "firstName",
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      id: "lastName",
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      id: "institution",
      accessorKey: "institution",
      header: "Institution",
      filterType: "select",
    },
    {
      id: "course",
      accessorKey: "course",
      header: "Course",
      filterType: "select",
    },
    {
      id: "phoneNumber",
      accessorKey: "phoneNumber",
      header: "Phone Number",
    },
    {
      id: "email",
      accessorKey: "email",
      header: "Email",
    },
    {
      id: "postingDate",
      accessorKey: "postingDate",
      header: "Posting Date",
      filterType: "date-range",
      cell: (info) => {
        const dateValue = info.getValue();
        return dateValue
          ? new Date(dateValue).toISOString().split("T")[0]
          : "N/A";
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      filterType: "select",
    },
    ...(activeFilter === "special"
      ? [
          {
            id: "specialReason",
            accessorKey: "specialReason",
            header: "Special Reason",
          },
        ]
      : []),
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "absolute",
                  right: 0,
                  top: "100%",
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedPersonnel(info.row.original);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <MainContent>
      <SectionHeader>PROFILE</SectionHeader>

      <InstitutionHeaderSection>
        <InstitutionLogo>
          <Image
            src="/profile-pic-placeholder.svg"
            alt="Company Logo"
            width={80}
            height={80}
            style={{ objectFit: "contain" }}
          />
        </InstitutionLogo>
        <InstitutionHeaderInfo>
          <InstitutionLabel>Company Name</InstitutionLabel>
          <InstitutionName>
            {company?.name || "Global Tech Solutions"}
          </InstitutionName>
        </InstitutionHeaderInfo>
      </InstitutionHeaderSection>

      <SectionHeader>PERSONNEL&apos;S POSTED TO COMPANY</SectionHeader>
      
      <TableContainer>
        <TableHeader>
          <HeaderActions>
            <TitleWrapper>
              <Users size={20} color="#6b7280" />
              <TableTitle>Posted Personnel</TableTitle>
            </TitleWrapper>
          </HeaderActions>
          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "regular"}
              onClick={() => handleFilterChange("regular")}
            >
              Regular Posting
            </FilterButton>
            <FilterButton
              $active={activeFilter === "special"}
              onClick={() => handleFilterChange("special")}
            >
              Special Posting
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {personnelData.isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading personnel data...</LoaderText>
            </LoaderContainer>
          ) : activeFilter === "regular" && personnelData.regular.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={40} color="#6b7280" />
              </EmptyStateIcon>
              <EmptyStateText>
                No regular postings found for this company
              </EmptyStateText>
            </EmptyState>
          ) : activeFilter === "special" && personnelData.special.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={40} color="#6b7280" />
              </EmptyStateIcon>
              <EmptyStateText>
                No special postings found for this company
              </EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              data={activeFilter === "regular" ? personnelData.regular : personnelData.special}
              columns={columns}
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
            />
          )}
        </TableContent>
      </TableContainer>
    </MainContent>
  );
}

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 2rem;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1.75rem;
`;

const HeaderActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => (props.$active ? "#16a34a" : "#6b7280")};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => (props.$active ? "#16a34a" : "transparent")};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: #16a34a;
  }
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;
