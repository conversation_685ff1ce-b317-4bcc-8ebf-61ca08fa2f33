// components/SearchBar.jsx
import React from "react";
import styled from "styled-components";

const SearchContainer = styled.div`
  position: relative;
`;

const SearchIcon = styled.div`
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding-left: 0.75rem;
  pointer-events: none;
`;

const Input = styled.input`
  width: 100%;
  @media (min-width: 768px) {
    width: 16rem;
  }
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;

  &:focus {
    outline: none;
    border-color: transparent;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.5);
  }
`;

const SearchBar = ({ value, onChange, placeholder = "Search..." }) => {
  return (
    <SearchContainer>
      <SearchIcon>
        <svg
          className="w-4 h-4 text-gray-500"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="#6b7280"
          width="16"
          height="16"
        >
          <path
            fillRule="evenodd"
            d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
            clipRule="evenodd"
          />
        </svg>
      </SearchIcon>
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
      />
    </SearchContainer>
  );
};

export default SearchBar;
