"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queries, mutations } from "@/api/client";
import {
  ChevronLeft,
  Download,
  Printer,
  MoreVertical,
  X,
  AlertTriangle,
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { toast } from "react-hot-toast";

export default function AdministratorDetails() {
  const params = useParams();
  const { push } = useRouter();
  const administratorId = params.administratorId;
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const queryClient = useQueryClient();
  const [isDisabling, setIsDisabling] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [showActivateModal, setShowActivateModal] = useState(false);

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Fetch administrator data
  const {
    data: administratorData,
    isLoading,
    error,
  } = useQuery({
    ...queries.getAdministrator(administratorId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Add the disable administrator mutation
  const disableAdministratorMutation = useMutation({
    ...mutations.disableAdministrator,
    onSuccess: () => {
      queryClient.invalidateQueries(["administrator", administratorId]);
      setIsDisabling(false);
      // Redirect back to the roles-users page after successful disabling
      setTimeout(() => {
        push("/dashboard/roles-users");
      }, 1500);
    },
    onError: (error) => {
      toast.error(
        error?.response?.data?.message || "Failed to disable administrator"
      );
      setIsDisabling(false);
    },
  });

  // Add the activate administrator mutation
  const activateAdministratorMutation = useMutation({
    ...mutations.activateAdministrator,
    onSuccess: () => {
      queryClient.invalidateQueries(["administrator", administratorId]);
      setIsActivating(false);
      // Redirect back to the roles-users page after successful activation
      setTimeout(() => {
        push("/dashboard/roles-users");
      }, 1500);
    },
    onError: (error) => {
      toast.error(
        error?.response?.data?.message || "Failed to activate administrator"
      );
      setIsActivating(false);
    },
  });

  // Handle disable button click
  const handleDisable = () => {
    setShowConfirmModal(true);
  };

  // Handle confirm disable
  const confirmDisable = () => {
    setShowConfirmModal(false);
    setIsDisabling(true);
    disableAdministratorMutation.mutate({ administratorId });
  };

  // Handle activate button click
  const handleActivate = () => {
    setShowActivateModal(true);
  };

  // Handle confirm activate
  const confirmActivate = () => {
    setShowActivateModal(false);
    setIsActivating(true);
    activateAdministratorMutation.mutate({ administratorId });
  };

  // Then update how we access the administrator data
  const administrator = administratorData?.data;

  console.log("Administrator data:", administrator);

  if (isLoading) {
    return (
      <Container>
        <LoaderContainer>
          <LoaderSpinner />
          <LoaderText>Loading administrator details...</LoaderText>
        </LoaderContainer>
      </Container>
    );
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <Container>
      <ActionContainer>
        <PageTitle>View Administrator Details</PageTitle>
        <ButtonGroup>
          <ActionButtonsContainer>
            {administrator?.archivedDate ? (
              <ActivateButton onClick={handleActivate} disabled={isActivating}>
                {isActivating ? "Activating..." : "Activate Administrator"}
              </ActivateButton>
            ) : (
              <DisableButton onClick={handleDisable} disabled={isDisabling}>
                {isDisabling ? "Disabling..." : "Disable Administrator"}
              </DisableButton>
            )}
          </ActionButtonsContainer>
          <DropdownContainer ref={dropdownRef}>
            <ActionButton
              variant="more"
              onClick={(e) => {
                e.stopPropagation();
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <MoreVertical size={16} />
            </ActionButton>
            {dropdownOpen && (
              <DropdownMenu>
                <DropdownItem
                  onClick={() => {
                    /* Add PDF download logic */
                    setDropdownOpen(false);
                  }}
                >
                  <Download size={16} />
                  Download as PDF
                </DropdownItem>
                <DropdownItem
                  onClick={() => {
                    window.print();
                    setDropdownOpen(false);
                  }}
                >
                  <Printer size={16} />
                  Print Page
                </DropdownItem>
              </DropdownMenu>
            )}
          </DropdownContainer>
        </ButtonGroup>
      </ActionContainer>
      <BackButtonContainer>
        <BackButton onClick={() => push("/dashboard/roles-users")}>
          <ChevronLeft size={16} />
          Back
        </BackButton>
      </BackButtonContainer>

      <MainContent>
        <SectionHeader>ADMINISTRATOR PROFILE</SectionHeader>

        <InstitutionHeaderSection>
          <InstitutionLogo>
            <Image
              src="/profile-pic-placeholder.svg"
              alt="Administrator Profile"
              width={80}
              height={80}
              style={{ objectFit: "contain" }}
            />
          </InstitutionLogo>
          <InstitutionHeaderInfo>
            <InstitutionLabel>Administrator Name</InstitutionLabel>
            <InstitutionName>
              {administrator?.fullName || "Administrator Name"}
            </InstitutionName>
          </InstitutionHeaderInfo>
        </InstitutionHeaderSection>

        <SectionHeader>ADMINISTRATOR DETAILS</SectionHeader>
        <DetailsGrid>
          <DetailItem>
            <DetailLabel>Full Name</DetailLabel>
            <DetailValue>{administrator?.fullName || "N/A"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Email Address</DetailLabel>
            <DetailValue>{administrator?.email || "N/A"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Phone Number</DetailLabel>
            <DetailValue>{administrator?.phoneNumber || "N/A"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Role</DetailLabel>
            <DetailValue>
              {administrator?.role
                ? administrator.role
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")
                : "N/A"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Region</DetailLabel>
            <DetailValue>{administrator?.region || "N/A"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Date Registered</DetailLabel>
            <DetailValue>
              {administrator?.createdAt
                ? new Date(administrator.createdAt).toISOString().split("T")[0]
                : "N/A"}
            </DetailValue>
          </DetailItem>
        </DetailsGrid>
      </MainContent>

      {(showConfirmModal || showActivateModal) && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>
                {showConfirmModal
                  ? "Confirm Disable Administrator"
                  : "Confirm Activate Administrator"}
              </ModalTitle>
              <CloseButton
                onClick={() =>
                  showConfirmModal
                    ? setShowConfirmModal(false)
                    : setShowActivateModal(false)
                }
              >
                <X size={18} />
              </CloseButton>
            </ModalHeader>
            <ModalBody>
              <WarningIcon
                style={{
                  backgroundColor: showConfirmModal ? "#fff7ed" : "#dcfce7",
                }}
              >
                <AlertTriangle
                  size={40}
                  color={showConfirmModal ? "#f59e0b" : "#16a34a"}
                />
              </WarningIcon>
              <ModalText>
                Are you sure you want to{" "}
                {showConfirmModal ? "disable" : "activate"} this administrator?
              </ModalText>
              <ModalDescription>
                {showConfirmModal
                  ? "This action will prevent the administrator from accessing the system. It can be reversed later if needed."
                  : "This action will allow the administrator to access the system again."}
              </ModalDescription>
            </ModalBody>
            <ModalFooter>
              <CancelButton
                onClick={() =>
                  showConfirmModal
                    ? setShowConfirmModal(false)
                    : setShowActivateModal(false)
                }
              >
                Cancel
              </CancelButton>
              <ConfirmButton
                onClick={showConfirmModal ? confirmDisable : confirmActivate}
                disabled={showConfirmModal ? isDisabling : isActivating}
                style={
                  !showConfirmModal
                    ? {
                        backgroundColor: "#16a34a",
                        borderColor: "#16a34a",
                      }
                    : {}
                }
              >
                {showConfirmModal
                  ? isDisabling
                    ? "Disabling..."
                    : "Yes, Disable"
                  : isActivating
                  ? "Activating..."
                  : "Yes, Activate"}
              </ConfirmButton>
            </ModalFooter>
          </ModalContent>
        </ModalOverlay>
      )}
    </Container>
  );
}

const Container = styled.div`
  min-height: 100vh;
  padding-bottom: 2rem;
  margin: 0 auto;
  background-color: #f5f7fa;
`;

const BackButtonContainer = styled.div`
  padding: 1rem 2rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #000;
  padding: 0;
`;

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
`;

const ActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e1e4ea;
`;

const PageTitle = styled.h1`
  font-size: 20px;
  font-weight: 500;
  color: #000;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  ${(props) =>
    props.variant === "more" &&
    `
    background: none;
    border: none;
    color: #666;
    padding: 0.5rem;
    &:hover {
      background-color: #f5f5f5;
    }
  `}
`;

const DropdownContainer = styled.div`
  position: relative;
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background-color: white;
  border: 1px solid #e1e4ea;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 1000;
`;

const DropdownItem = styled.button`
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const ActionButtonsContainer = styled.div`
  /* margin-top: 2rem; */
  display: flex;
  justify-content: flex-end;
`;

const DisableButton = styled.button`
  padding: 0.75rem 0.75rem;
  background-color: ${(props) => (props.disabled ? "#cccccc" : "#ef4444")};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  transition: background-color 0.2s;

  &:hover {
    background-color: ${(props) => (props.disabled ? "#cccccc" : "#dc2626")};
  }
`;

const ActivateButton = styled.button`
  padding: 0.75rem 0.75rem;
  background-color: ${(props) => (props.disabled ? "#cccccc" : "#16a34a")};
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  transition: background-color 0.2s;

  &:hover {
    background-color: ${(props) => (props.disabled ? "#cccccc" : "#15803d")};
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 8px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 0.25rem;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const WarningIcon = styled.div`
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #fff7ed;
`;

const ModalText = styled.p`
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: #111827;
  text-align: center;
`;

const ModalDescription = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
`;

const CancelButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #e5e7eb;
  }
`;

const ConfirmButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: ${(props) => (props.disabled ? "#f87171" : "#ef4444")};
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};

  &:hover {
    background-color: ${(props) => (props.disabled ? "#f87171" : "#dc2626")};
  }
`;
