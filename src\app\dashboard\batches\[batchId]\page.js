"use client";
import { useParams } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { queries, api } from "@/api/client";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import {
  FileText,
  Users,
  Calendar,
  InboxIcon,
  MoreVertical,
} from "lucide-react";
import { DataTable } from "@globalicons/enterprise-tools";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";
import ActivationModal from "@/components/dashboard/modals/ActivationModal";

// Styled components matching the students page design
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1.75rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const BatchInfoContainer = styled.div`
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
`;

const InfoCard = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const InfoLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const InfoValue = styled.div`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
`;

const ActionButton = styled.button`
  background-color: ${(props) => (props.disabled ? "#cccccc" : "#16a34a")};
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  transition: background-color 0.2s;
  margin-left: auto;

  &:hover {
    background-color: ${(props) => (props.disabled ? "#cccccc" : "#15803d")};
  }
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

const HeaderActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => (props.$active ? "#16a34a" : "#6b7280")};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => (props.$active ? "#16a34a" : "transparent")};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: #16a34a;
  }
`;

const StudentsInBatchTable = () => {
  const params = useParams();
  const { batchId } = params;
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [isActivating, setIsActivating] = useState(false);
  // Add these missing state variables
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [showExtendDateModal, setShowExtendDateModal] = useState(false);
  const [activeFilter, setActiveFilter] = useState("all");
  const [isExtending, setIsExtending] = useState(false);
  // Add state for match students
  const [isMatching, setIsMatching] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  // Fetch batch details
  const { data: batchData, isLoading: isBatchLoading } = useQuery({
    queryKey: ["batch", batchId],
    queryFn: () => api.getBatch({ batchId }),
    enabled: !!sessionStorage.getItem("authToken") && !!batchId,
  });

  // Enhanced auth check
  useEffect(() => {
    const token = sessionStorage.getItem("authToken");
    if (!token) {
      push("/login");
    } else {
      queryClient.invalidateQueries(["studentsInBatch"]);
      queryClient.invalidateQueries(["batch", batchId]);
    }
  }, [push, queryClient, batchId]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []); // Remove openDropdownId from dependency array

  // Activate batch mutation
  const activateBatchMutation = useMutation({
    mutationFn: ({ startDate, endDate }) =>
      api.activateBatch({
        batchId,
        registrationStartDate: startDate,
        registrationEndDate: endDate,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(["studentsInBatch"]);
      queryClient.invalidateQueries(["batch", batchId]);
      setIsActivating(false);
      alert("Batch activated successfully!");
    },
    onError: (error) => {
      setIsActivating(false);
      alert(`Error activating batch: ${error.message}`);
    },
  });

  // Handle activate batch button click
  const handleActivateBatch = () => {
    setShowActivateModal(true);
  };

  const confirmActivation = ({ startDate, endDate }) => {
    setIsActivating(true);
    activateBatchMutation.mutate({ startDate, endDate });
    setShowActivateModal(false);
  };

  // Add match students mutation
  const matchStudentsMutation = useMutation({
    mutationFn: () => api.matchStudents(batchId),
    onSuccess: () => {
      queryClient.invalidateQueries(["studentsInBatch"]);
      queryClient.invalidateQueries(["batch", batchId]);
      setIsMatching(false);
      alert("Students matched successfully!");
    },
    onError: (error) => {
      setIsMatching(false);
      alert(`Error matching students: ${error.message}`);
    },
  });

  // Handle match students button click
  const handleMatchStudents = () => {
    if (confirm("Are you sure you want to match students to postings?")) {
      setIsMatching(true);
      matchStudentsMutation.mutate();
    }
  };

  // Add extend date mutation
  const extendDateMutation = useMutation({
    mutationFn: ({ endDate }) =>
      api.updateBatch(batchId, {
        registrationEndDate: endDate,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries(["batch", batchId]);
      setIsExtending(false);
      alert("Registration end date extended successfully!");
    },
    onError: (error) => {
      setIsExtending(false);
      alert(`Error extending end date: ${error.message}`);
    },
  });

  // Handle extend date button click
  const handleExtendDate = () => {
    setShowExtendDateModal(true);
  };

  const confirmExtendDate = ({ endDate }) => {
    setIsExtending(true);
    extendDateMutation.mutate({ endDate });
    setShowExtendDateModal(false);
  };

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  // Fetch students in batch data
  const {
    data: studentsData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listStudentsInBatch,
    queryFn: () => queries.listStudentsInBatch.queryFn({ batchId }),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken") && !!batchId,
  });

  console.log("studentsData", studentsData);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  // Define columns configuration
  const columns = [
    {
      id: "passportPhoto",
      accessorKey: "passportPhoto",
      header: "Photo",
      size: 80,
      cell: (info) => {
        const photoUrl = info.getValue();
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              width: "40px",
              height: "40px",
              borderRadius: "50%",
              overflow: "hidden",
              backgroundColor: "#f3f4f6",
            }}
          >
            <img
              src={photoUrl || "/profile-pic-placeholder.svg"}
              alt="Student Photo"
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </div>
        );
      },
    },
    {
      id: "studentId",
      accessorKey: "studentId",
      header: "Student ID",
    },
    {
      id: "firstName",
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      id: "middleName",
      accessorKey: "middleName",
      header: "Middle Name",
    },
    {
      id: "lastName",
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      id: "institution",
      accessorKey: "institution",
      header: "Institution",
      filterType: "select",
    },

    {
      id: "course",
      accessorKey: "course",
      header: "Course",
      filterType: "select",
      cell: (info) => {
        const courseData = info.getValue();
        return courseData?.name || "N/A";
      },
    },

    {
      id: "phoneNumber",
      accessorKey: "phoneNumber",
      header: "Phone Number",
      size: 150,
    },
    {
      id: "email",
      accessorKey: "emailAddress",
      header: "Email",
    },
    {
      id: "documents",
      accessorKey: "documents",
      header: "Supporting Document",
      type: "pdf",
    },
    {
      id: "nssNumber",
      accessorKey: "nssNumber",
      header: "NSS Number",
    },
    {
      id: "enrollmentStatus",
      accessorKey: "enrollmentStatus",
      header: "Enrollment Status",
      filterType: "select",
      cell: (info) => {
        const enrollmentStatus = info.getValue();
        if (!enrollmentStatus) return "N/A";

        let color;
        // Convert to lowercase for case-insensitive comparison
        const status = enrollmentStatus.toLowerCase();

        switch (status) {
          case "awaiting posting":
            color = "#16a34a"; // green
            break;
          case "awaiting registration":
            color = "#f59e0b"; // amber
            break;
          case "details submitted":
            color = "#6b7280"; // gray
            break;
          case "deferred":
            color = "#dc2626"; // red-600 for deferred status
            break;
          default:
            color = "#3b82f6"; // blue for other statuses
        }

        // For debugging
        console.log("Enrollment Status:", enrollmentStatus, "Color:", color);

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {enrollmentStatus.charAt(0).toUpperCase() +
              enrollmentStatus.slice(1).toLowerCase()}
          </span>
        );
      },
    },
    {
      id: "companyPostedTo",
      accessorKey: "posting",
      header: "Company Posted To",
      size: 150,
      filterType: "select",
      cell: (info) => {
        const posting = info.getValue();
        return posting.company || "Not Assigned";
      },
    },
    {
      id: "regionPostedTo",
      accessorKey: "posting",
      header: "Region Posted To",
      size: 150,
      filterType: "select",
      cell: (info) => {
        const posting = info.getValue();
        return posting.region || "Not Assigned";
      },
    },
    {
      id: "dateRegistered",
      accessorKey: "dateRegistered",
      header: "Date Registered",
      filterType: "date-range",
      cell: (info) => {
        const dateValue = info.getValue();
        return dateValue
          ? new Date(dateValue).toISOString().split("T")[0]
          : "N/A";
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left by 100px
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  // minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedStudent(info.row.original);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleArchive(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                    color: "#ef4444",
                  }}
                >
                  Archive
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  const filteredStudents = React.useMemo(() => {
    if (!studentsData?.data) return [];

    switch (activeFilter) {
      case "registration":
        return studentsData.data.filter(
          (student) => student.enrollmentStatus === "Awaiting Registration"
        );
      case "registered":
        return studentsData.data.filter(
          (student) =>
            student.enrollmentStatus === "Awaiting Posting" ||
            student.enrollmentStatus === "Posted"
        );
      case "deferments":
        const deferredStudents = studentsData.data.filter(
          (student) => student.enrollmentStatus === "Deferred"
        );
        return deferredStudents.length > 0 ? deferredStudents : null; // Return null if empty to show EmptyState
      default:
        return studentsData.data;
    }
  }, [studentsData?.data, activeFilter]);

  const batch = batchData?.data;

  return (
    <Container>
      <PageTitle>Enrollment Details</PageTitle>

      {isBatchLoading ? (
        <LoaderContainer>
          <LoaderSpinner />
          <LoaderText>Loading enrollment details...</LoaderText>
        </LoaderContainer>
      ) : (
        <>
          <BatchInfoContainer>
            <InfoCard>
              <InfoLabel>
                <Calendar size={16} /> Enrollment Name
              </InfoLabel>
              <InfoValue>{batch?.name || "Unnamed Batch"}</InfoValue>
            </InfoCard>
            <InfoCard>
              <InfoLabel>
                <Calendar size={16} /> Start Date
              </InfoLabel>
              <InfoValue>{formatDate(batch?.startDate)}</InfoValue>
            </InfoCard>
            <InfoCard>
              <InfoLabel>
                <Calendar size={16} /> End Date
              </InfoLabel>
              <InfoValue>{formatDate(batch?.endDate)}</InfoValue>
            </InfoCard>
            <InfoCard>
              <InfoLabel>
                <Users size={16} /> Personnel
              </InfoLabel>
              <InfoValue>{studentsData?.data?.length || 0}</InfoValue>
            </InfoCard>
            <InfoCard>
              <InfoLabel>
                <Calendar size={16} /> Status
              </InfoLabel>
              <InfoValue
                style={{
                  color:
                    batch?.stage === "enrollment"
                      ? "#16a34a"
                      : batch?.stage === "registration"
                      ? "#f59e0b"
                      : "#ef4444",
                }}
              >
                {batch?.stage
                  ? batch.stage.charAt(0).toUpperCase() + batch.stage.slice(1)
                  : "Pending"}
              </InfoValue>
            </InfoCard>

            {batch?.stage === "enrollment" ? (
              <ActionButton
                onClick={handleActivateBatch}
                disabled={isActivating}
              >
                {isActivating ? "Activating..." : "Activate Batch"}
              </ActionButton>
            ) : batch?.stage === "registration" ? (
              <div style={{ display: "flex", gap: "1rem", marginLeft: "auto" }}>
                <ActionButton
                  onClick={handleExtendDate}
                  disabled={isExtending}
                  style={{
                    backgroundColor: isExtending ? "#cccccc" : "#f59e0b",
                    marginLeft: 0,
                    fontWeight: "600",
                  }}
                >
                  {isExtending ? "Extending..." : "Extend End Date"}
                </ActionButton>
                <ActionButton
                  onClick={handleMatchStudents}
                  disabled={isMatching}
                  style={{
                    backgroundColor: isMatching ? "#cccccc" : "#3b82f6",
                    marginLeft: 0,
                    fontWeight: "600",
                  }}
                >
                  {isMatching ? "Posting Students..." : "Post Students"}
                </ActionButton>
                <ActionButton disabled={true}>
                  Registration in Progress
                </ActionButton>
              </div>
            ) : (
              <div style={{ display: "flex", gap: "1rem", marginLeft: "auto" }}>
                <ActionButton disabled={true}>Batch Activated</ActionButton>
              </div>
            )}
          </BatchInfoContainer>

          <TableContainer>
            <TableHeader>
              <HeaderActions>
                <TitleWrapper>
                  <Users size={20} color="#6b7280" />
                  <TableTitle>Personnel In Enrollment</TableTitle>
                </TitleWrapper>
              </HeaderActions>
              <ButtonGroup>
                <FilterButton
                  $active={activeFilter === "all"}
                  $type="all"
                  onClick={() => handleFilterChange("all")}
                >
                  All
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "registration"}
                  $type="registration"
                  onClick={() => handleFilterChange("registration")}
                >
                  Registration in Progress
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "registered"}
                  $type="registered"
                  onClick={() => handleFilterChange("registered")}
                >
                  Registered
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "deferments"}
                  $type="deferments"
                  onClick={() => handleFilterChange("deferments")}
                >
                  Deferments
                </FilterButton>
              </ButtonGroup>
            </TableHeader>

            <TableContent>
              {isLoading ? (
                <LoaderContainer>
                  <LoaderSpinner />
                  <LoaderText>Loading students...</LoaderText>
                </LoaderContainer>
              ) : error ? (
                <div
                  style={{
                    padding: "2rem",
                    textAlign: "center",
                    color: "#ef4444",
                  }}
                >
                  Error loading students: {error.message}
                </div>
              ) : filteredStudents === null ? (
                <EmptyState>
                  <EmptyStateIcon>
                    <InboxIcon size={64} color="#9ca3af" />
                  </EmptyStateIcon>
                  <EmptyStateText>
                    {activeFilter === "all"
                      ? "No students found in this batch"
                      : activeFilter === "registration"
                      ? "No students awaiting registration"
                      : activeFilter === "registered"
                      ? "No registered students found"
                      : "No deferred students found"}
                  </EmptyStateText>
                </EmptyState>
              ) : (
                <DataTable
                  data={filteredStudents || []}
                  columns={columns}
                  theme="nss"
                  enableColumnVisibility
                  enableRowSelection
                  enablePagination
                  enableFilters
                  isSearchable
                  pageSize={10}
                  isDownloadable={{ formats: ["csv", "pdf"] }}
                  onRowClick={(row) => {
                    setOpenPeakDrawer(false);
                    setSelectedStudent(row);
                    setOpenPeakDrawer(true);
                  }}
                />
              )}
            </TableContent>
          </TableContainer>

          {/* Add PeakDetails component */}
          <PeakDetails
            student={selectedStudent}
            isOpen={openPeakDrawer}
            onClose={() => setOpenPeakDrawer(false)}
          />
        </>
      )}
      <ActivationModal
        isOpen={showActivateModal}
        onClose={() => setShowActivateModal(false)}
        onConfirm={confirmActivation}
        isLoading={isActivating}
      />

      {/* Add ExtendDateModal */}
      <ActivationModal
        isOpen={showExtendDateModal}
        onClose={() => setShowExtendDateModal(false)}
        onConfirm={confirmExtendDate}
        isLoading={isExtending}
        isExtendMode={true}
      />
    </Container>
  );
};

export default StudentsInBatchTable;
