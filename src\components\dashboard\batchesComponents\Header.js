// components/Header.jsx
import React from "react";
import styled from "styled-components";

const HeaderContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const IconContainer = styled.div`
  width: 1.5rem;
  height: 1.5rem;
  background-color: #10b981;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Icon = styled.svg`
  height: 1rem;
  width: 1rem;
  color: white;
`;

const Title = styled.h1`
  font-size: 1.25rem;
  font-weight: 700;
`;

const Header = ({ title }) => {
  return (
    <HeaderContainer>
      <IconContainer>
        <Icon
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 6a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zm0 6a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z"
            clipRule="evenodd"
          />
        </Icon>
      </IconContainer>
      <Title>{title}</Title>
    </HeaderContainer>
  );
};

export default Header;
