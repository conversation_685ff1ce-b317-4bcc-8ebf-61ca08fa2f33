// app/components/dashboard/AccountSidebar.js
import Link from "next/link";
import { Nav, NavTitle, NavList } from "../styles/dashboard/account.styled";

const SidebarNav = () => (
  <Nav>
    <NavList>
      <NavTitle>
        <Link href="/dashboard/settings">Account Details</Link>
      </NavTitle>
      <NavTitle>
        <Link href="/dashboard/settings/active-sessions">Active Sessions</Link>
      </NavTitle>
      <NavTitle>
        <Link href="/dashboard/settings/change-password">Change Password</Link>
      </NavTitle>
    </NavList>
  </Nav>
);

export default SidebarNav;
