// src/components/DataTable/TableRow.js
import React, { useState, useRef, useEffect } from "react";
import styled, { css } from "styled-components";

// Styled Components
const Row = styled.tr`
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }

  ${(props) =>
    props.selected &&
    css`
      background-color: #eff6ff;
    `}
`;

const Cell = styled.td`
  padding: 0.75rem;
  font-size: 0.875rem;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: #374151;
  ${(props) =>
    props.style &&
    css`
      min-width: ${props.style.minWidth};
      max-width: ${props.style.maxWidth};
      width: ${props.style.width};
    `}
`;

const CheckboxCell = styled.td`
  padding: 0.75rem;
`;

const ActionsCell = styled.td`
  padding: 0.75rem;
  position: relative;
`;

const Checkbox = styled.input`
  width: 1rem;
  height: 1rem;
  color: #2563eb;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
`;

const ActionButton = styled.button`
  all: unset;

  color: #6b7280;

  &:hover {
    color: #374151;
  }
`;

const ActionMenu = styled.div`
  position: absolute;
  right: 0;
  margin-top: 0.5rem;
  max-width: fit-content;
  padding: 4px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 10;
`;

const ActionMenuItem = styled.button`
  all: unset;

  display: block;
  min-width: 12em;
  border-radius: 6px;
  text-align: left;
  padding: 1rem;
  font-size: 0.875rem;
  color: ${(props) => (props.danger ? "#dc2626" : "#374151")};

  &:hover {
    background-color: #f3f4f6;
  }
`;

const NumberValue = styled.span`
  color: #059669;
  font-weight: 500;
`;

const PdfTag = styled.span`
  background-color: #fee2e2;
  color: #991b1b;
  padding: 0 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-right: 0.5rem;
`;

const PdfContainer = styled.div`
  display: flex;
  align-items: center;
`;

const LinkText = styled.a`
  color: #2563eb;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

/**
 * TableRow - Renders a data row with selectable checkbox and actions menu
 * @param {Object} props - Component props
 * @param {Object} props.rowData - Data object for this row
 * @param {Array} props.columns - Array of column configuration objects
 * @param {Boolean} props.selected - Whether this row is selected
 * @param {Function} props.onSelect - Function to call when row selection changes
 * @param {Function} props.onClick - Function to call when row is clicked
 */
const TableRow = ({ rowData, columns, selected, onSelect, onClick }) => {
  const [showActions, setShowActions] = useState(false);
  const actionMenuRef = useRef(null);
  const actionButtonRef = useRef(null);

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        showActions && 
        actionMenuRef.current && 
        !actionMenuRef.current.contains(event.target) &&
        !actionButtonRef.current.contains(event.target)
      ) {
        setShowActions(false);
      }
    }

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);
    
    // Clean up
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showActions]);

  // Format cell value based on column type
  const renderCell = (column, value) => {
    if (column.render) {
      return column.render(value, rowData);
    }

    switch (column.type) {
      case "number":
        return <NumberValue>{value}</NumberValue>;
      case "pdf":
        return (
          <PdfContainer>
            <PdfTag>PDF</PdfTag>
            {value}
          </PdfContainer>
        );
      case "link":
        return (
          <LinkText href="/dashboard/students?school=legon">{value}</LinkText>
        );
      default:
        return value;
    }
  };

  return (
    <Row 
      selected={selected} 
      onClick={onClick} // Add the onClick handler to the Row component
      style={{ cursor: onClick ? 'pointer' : 'default' }} // Add cursor style to indicate clickability
    >
      {/* Selection checkbox */}
      <CheckboxCell onClick={(e) => e.stopPropagation()}>
        <Checkbox type="checkbox" checked={selected} onChange={onSelect} />
      </CheckboxCell>

      {/* Data cells */}
      {columns.map((column) => (
        <Cell key={column.key} style={column.style}>
          {renderCell(column, rowData[column.key])}
        </Cell>
      ))}

      {/* Actions menu */}
      <ActionsCell onClick={(e) => e.stopPropagation()}>
        <ActionButton 
          ref={actionButtonRef}
          onClick={() => setShowActions(!showActions)}
        >
          <svg
            style={{ width: "1.25rem", height: "1.25rem" }}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </ActionButton>

        {showActions && (
          <ActionMenu ref={actionMenuRef}>
              <ActionMenuItem>View Details</ActionMenuItem>
              <ActionMenuItem>Download Document</ActionMenuItem>
              <ActionMenuItem>View Attendance History</ActionMenuItem>
              <ActionMenuItem danger>Archive </ActionMenuItem>
          </ActionMenu>
        )}
      </ActionsCell>
    </Row>
  );
};

export default TableRow;
