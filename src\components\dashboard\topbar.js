import React, { useEffect, useState, useRef } from "react";
import PropTypes from "prop-types";
import styled from "styled-components";
import { usePathname, useRouter } from "next/navigation";
import { LogOut, X, AlertTriangle } from "lucide-react";

// Styled Components
const TopBarContainer = styled.div`
  background-color: ${(props) => props.backgroundColor};
  color: ${(props) => props.textColor};
  border-bottom: 1px solid ${(props) => props.borderColor};
  padding: ${(props) => props.padding};
  font-family: sans-serif;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
`;

const PageTitle = styled.div`
  font-size: 28px;
  font-weight: 500;
`;

const RightContentContainer = styled.div`
  display: flex;
  align-items: center;
`;

const NotificationContainer = styled.div`
  position: relative;
  margin-right: 20px;
`;

const NotificationButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const NotificationBadge = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  background-color: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
`;

const NotificationDropdown = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  width: 450px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-top: 8px;
  z-index: 1000;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #eee;
`;

const NotificationHeader = styled.div`
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
`;

const NotificationTitle = styled.h3`
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
`;

const MarkAllReadButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
`;

const NotificationList = styled.div`
  padding: 0;
`;

const NotificationItem = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  position: relative;
  display: flex;
  gap: 12px;
`;

const NotificationIcon = styled.div`
  background-color: ${(props) => {
    switch (props.type) {
      case "success":
        return "#ecfdf5";
      case "warning":
        return "#fffbeb";
      case "error":
        return "#fef2f2";
      default:
        return "#eff6ff";
    }
  }};
  color: ${(props) => {
    switch (props.type) {
      case "success":
        return "#059669";
      case "warning":
        return "#d97706";
      case "error":
        return "#dc2626";
      default:
        return "#3b82f6";
    }
  }};
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationContentHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 8px;
`;

const UnreadIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ef4444;
  margin-top: 6px;
  flex-shrink: 0;
`;

const NotificationItemTitle = styled.div`
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.4;
`;

const NotificationItemSubtitle = styled.div`
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
`;

const NotificationFooter = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const NotificationTime = styled.div`
  font-size: 13px;
  color: #9ca3af;
`;

const NotificationAction = styled.a`
  font-size: 14px;
  color: #000;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const EmptyNotification = styled.div`
  padding: 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
`;

const DropdownFooter = styled.div`
  padding: 12px 16px;
  border-top: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #6b7280;
  font-size: 13px;
`;

const NavigationHint = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const KeyboardKey = styled.div`
  width: 24px;
  height: 24px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SeeAllLink = styled.a`
  color: #16a34a;
  text-decoration: none;
  font-weight: 500;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid #ef4444;
  background-color: transparent;
  color: #ef4444;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #fef2f2;
  }

  &:active {
    background-color: #fee2e2;
  }
`;

// Add these new styled components for the modal
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 8px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border-radius: 0.25rem;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const ModalBody = styled.div`
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const WarningIcon = styled.div`
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: #fff7ed;
`;

const ModalText = styled.p`
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 500;
  color: #111827;
  text-align: center;
`;

const ModalDescription = styled.p`
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: center;
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e5e7eb;
`;

const CancelButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #e5e7eb;
  }
`;

const ConfirmButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #dc2626;
  }
`;

const TopBar = ({
  pageTitle = "Dashboard Overview",
  backgroundColor = "#ffffff",
  textColor = "#000000",
  borderColor = "#f3f3f3",
  padding = "1rem 2rem",
  fontSize = "1.5rem",
  fontWeight = "500",
  rightContent = null,
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [notificationCount, setNotificationCount] = useState(); // Set to 3 for demo
  const notificationRef = useRef(null);
  const pathname = usePathname();
  const router = useRouter();
  // Add state for logout confirmation modal
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: "New personnel enrollment request received",
      subtitle:
        "Ghana University has submitted 15 new personnel for the 2024/2025 batch.",
      time: "8 min ago",
      read: false,
      type: "info",
      action: null,
    },
    {
      id: 2,
      title: "Personnel deployment completed",
      subtitle:
        "All 32 personnel have been successfully assigned to their respective agencies.",
      time: "10 min ago",
      read: false,
      type: "success",
      action: {
        text: "View Postings",
        url: "/dashboard/postings",
      },
    },
    {
      id: 3,
      title: "Agency registration deadline approaching",
      subtitle:
        "5 agencies have not completed their annual registration. Deadline: March 30, 2025.",
      time: "10 min ago",
      read: false,
      type: "warning",
      action: {
        text: "Complete Application",
        url: "/dashboard/application",
      },
    },
  ]);

  // Check if we're on a settings page
  const isSettingsPage = pathname?.includes("/dashboard/settings");

  // Handle logout button click - now opens the confirmation modal
  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  // Handle actual logout after confirmation
  const handleLogout = () => {
    // Clear session storage
    sessionStorage.removeItem("authToken");
    sessionStorage.removeItem("user");

    // Redirect to login page
    router.push("/login");
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
    }

    // Add event listener when dropdown is shown
    if (showNotifications) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Cleanup the event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showNotifications]);

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map((notification) => ({
      ...notification,
      read: true,
    }));
    setNotifications(updatedNotifications);
    setNotificationCount(0);
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case "success":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "warning":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 9V11M12 15H12.01M5.07183 19H18.9282C20.4678 19 21.4301 17.3333 20.6603 16L13.7321 4C12.9623 2.66667 11.0378 2.66667 10.268 4L3.33978 16C2.56998 17.3333 3.53223 19 5.07183 19Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "error":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 14L12 12M12 12L14 10M12 12L10 10M12 12L14 14M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        );
      default: // info
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13 16H12V12H11M12 8H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        );
    }
  };

  return (
    <TopBarContainer
      backgroundColor={backgroundColor}
      textColor={textColor}
      borderColor={borderColor}
      padding={padding}
      className="topbar"
    >
      <PageTitle
        fontSize={fontSize}
        fontWeight={fontWeight}
        className="page-title"
      >
        {pageTitle}
      </PageTitle>

      {/* Right section with notification icon or logout button */}
      <RightContentContainer className="right-content">
        {isSettingsPage ? (
          // Show logout button on settings pages
          <LogoutButton onClick={handleLogoutClick} title="Log Out">
            Log Out
            <LogOut size={16} />
          </LogoutButton>
        ) : (
          // Show notification bell on other pages
          <NotificationContainer
            className="notification-container"
            ref={notificationRef}
          >
            <NotificationButton onClick={toggleNotifications}>
              {/* Bell Icon SVG */}
              <img
                src="/icons/notification-3-line.svg"
                alt="Notifications"
                width="24"
                height="24"
              />

              {/* Notification Badge */}
              {notificationCount > 0 && (
                <NotificationBadge>
                  {notificationCount > 9 ? "9+" : notificationCount}
                </NotificationBadge>
              )}
            </NotificationButton>

            {/* Notification Dropdown */}
            {showNotifications && (
              <NotificationDropdown>
                <NotificationHeader>
                  <NotificationTitle>Notifications</NotificationTitle>
                  <MarkAllReadButton onClick={markAllAsRead}>
                    Mark all as read
                  </MarkAllReadButton>
                </NotificationHeader>

                <NotificationList>
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <NotificationItem key={notification.id}>
                        <NotificationIcon type={notification.type}>
                          {getTypeIcon(notification.type)}
                        </NotificationIcon>

                        <NotificationContent>
                          <NotificationContentHeader>
                            {!notification.read && <UnreadIndicator />}
                            <div style={{ flex: 1 }}>
                              <NotificationItemTitle>
                                {notification.title}
                              </NotificationItemTitle>
                              <NotificationItemSubtitle>
                                {notification.subtitle}
                              </NotificationItemSubtitle>

                              <NotificationFooter>
                                <NotificationTime>
                                  {notification.time}
                                </NotificationTime>

                                {notification.action && (
                                  <NotificationAction
                                    href={notification.action.url}
                                  >
                                    {notification.action.text}
                                    <svg
                                      width="16"
                                      height="16"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M9 18L15 12L9 6"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                    </svg>
                                  </NotificationAction>
                                )}
                              </NotificationFooter>
                            </div>
                          </NotificationContentHeader>
                        </NotificationContent>
                      </NotificationItem>
                    ))
                  ) : (
                    <EmptyNotification>No notifications</EmptyNotification>
                  )}
                </NotificationList>

                <DropdownFooter>
                  <NavigationHint>
                    Use
                    <KeyboardKey>
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 19V5M12 5L5 12M12 5L19 12"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </KeyboardKey>
                    <KeyboardKey>
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 5V19M12 19L5 12M12 19L19 12"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </KeyboardKey>
                    to navigate
                  </NavigationHint>

                  <SeeAllLink href="/dashboard/notifications">
                    See All
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M9 18L15 12L9 6"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </SeeAllLink>
                </DropdownFooter>
              </NotificationDropdown>
            )}
          </NotificationContainer>
        )}
        {rightContent}
      </RightContentContainer>

      {/* Logout Confirmation Modal */}
      {showLogoutModal && (
        <ModalOverlay>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Confirm Logout</ModalTitle>
              <CloseButton onClick={() => setShowLogoutModal(false)}>
                <X size={18} />
              </CloseButton>
            </ModalHeader>
            <ModalBody>
              <WarningIcon>
                <AlertTriangle size={40} color="#f59e0b" />
              </WarningIcon>
              <ModalText>Are you sure you want to log out?</ModalText>
              <ModalDescription>
                You will need to sign in again to access your account.
              </ModalDescription>
            </ModalBody>
            <ModalFooter>
              <CancelButton onClick={() => setShowLogoutModal(false)}>
                Cancel
              </CancelButton>
              <ConfirmButton onClick={handleLogout}>Yes, Log Out</ConfirmButton>
            </ModalFooter>
          </ModalContent>
        </ModalOverlay>
      )}
    </TopBarContainer>
  );
};

TopBar.propTypes = {
  pageTitle: PropTypes.string,
  backgroundColor: PropTypes.string,
  textColor: PropTypes.string,
  borderColor: PropTypes.string,
  padding: PropTypes.string,
  fontSize: PropTypes.string,
  fontWeight: PropTypes.string,
  rightContent: PropTypes.node,
  notificationCount: PropTypes.number,
};

export default TopBar;
