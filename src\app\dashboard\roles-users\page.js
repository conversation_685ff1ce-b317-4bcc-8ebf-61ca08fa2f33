"use client";

import React, { useState, useMemo, useEffect, useRef } from "react";
import styled from "styled-components";
import { DataTable } from "@globalicons/enterprise-tools";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  FileText,
  Shield,
  User,
  Phone,
  Mail,
  Globe,
  MapPin,
  X,
  Users,
} from "lucide-react";
import toast from "react-hot-toast";
import Select from "react-select";
import { queries, mutations } from "@/api/client";
import { useRouter } from "next/navigation";

import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const AddUserButton = styled.button`
  background-color: #16a34a;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #15803d;
  }
`;

const TableWrapperContainer = styled.div`
  // Renamed from TableContainer to avoid conflict if you copy more
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem; // Adjusted gap
`;

const HeaderTopRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0.75rem; // Space before filter buttons
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => (props.$active ? "#16a34a" : "#6b7280")};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => (props.$active ? "#16a34a" : "transparent")};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: #16a34a;
  }
`;

const AddUserModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const AddUserModalContent = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const AddUserModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const AddUserModalTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const TableContent = styled.div`
  // If you need specific padding for the table itself, like in students page
  // padding: 1rem;
  // For now, DataTable might handle its own padding or this can be added if needed
`;

const FormContainer = styled.form`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
  padding: 0 0.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  grid-column: ${(props) => (props.fullWidth ? "1 / span 2" : "auto")};
`;

const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const InputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const IconWrapper = styled.div`
  position: absolute;
  left: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
`;

const FormInput = styled.input`
  padding: 0.625rem 0.625rem 0.625rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #111827;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 1px #16a34a;
  }
`;

const FormSelect = styled.select`
  padding: 0.625rem 0.625rem 0.625rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 1px #16a34a;
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  grid-column: 1 / span 2;
  margin-top: 1rem;
`;

const Button = styled.button`
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  ${(props) =>
    props.$primary &&
    `
    background-color: #16a34a;
    color: white;
    border: none;
    
    &:hover {
      background-color: #15803d;
    }
  `}

  ${(props) =>
    props.$secondary &&
    `
    background-color: white;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background-color: #f9fafb;
    }
  `}
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

// Add this styled component for the Select
const StyledSelect = styled(Select)`
  width: 100%;
  font-size: 0.875rem;

  .react-select__control {
    border-color: #d1d5db;
    border-radius: 0.375rem;
    min-height: 2.5rem;
    box-shadow: none;
    padding-left: 2rem;
  }

  .react-select__control:hover {
    border-color: #9ca3af;
  }

  .react-select__control--is-focused {
    border-color: #16a34a !important;
    box-shadow: 0 0 0 1px #16a34a !important;
  }

  .react-select__placeholder {
    color: #9ca3af;
  }

  .react-select__indicator-separator {
    background-color: #d1d5db;
  }

  .react-select__menu {
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 100;
  }

  .react-select__option {
    padding: 0.5rem 1rem;
  }

  .react-select__option--is-focused {
    background-color: #f3f4f6;
  }

  .react-select__option--is-selected {
    background-color: #16a34a;
    color: white;
  }
`;

// Update columns for the DataTable to match API data structure

export default function RolesAndUsersPage() {
  const queryClient = useQueryClient();
  const { push } = useRouter();
  const [currentUserRole, setCurrentUserRole] = useState(null);

  const [activeFilter, setActiveFilter] = useState("national"); // Default to 'national' instead of 'regional'
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedAdministrator, setSelectedAdministrator] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [districts, setDistricts] = useState([]);

  // Get current user role
  useEffect(() => {
    const user = JSON.parse(sessionStorage.getItem("user") || "{}");
    setCurrentUserRole(user.role || null);

    // Set default filter based on role
    if (user.role === "regional_administrator") {
      setActiveFilter("regional");
    }
  }, []);

  // Form state
  const [formData, setFormData] = useState({
    role: "",
    fullName: "",
    email: "",
    phoneNumber: "",
    region: "",
    district: null,
  });

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // If the role changes to nss_officer and we have a region, fetch districts
    if (name === "role" && value === "nss_officer" && formData.region) {
      queryClient.invalidateQueries({
        queryKey: ["districts"],
      });
    }

    // If the region changes and the role is already nss_officer, fetch districts
    if (name === "region" && formData.role === "nss_officer") {
      queryClient.invalidateQueries({
        queryKey: ["districts"],
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      role: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      region: "",
      district: null,
    });
  };

  const {
    data: administratorsData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listAdministrators,
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  const addAdministratorMutation = useMutation({
    ...mutations.addAdministrators,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["administrators"] });
      setShowAddUserModal(false);
      resetForm();
      toast.success("Administrator added successfully!");
    },
    onError: (error) => {
      toast.error(
        error?.response?.data?.message || "Failed to add administrator"
      );
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const {
    data: districtsData,
    isLoading: isLoadingDistricts,
    error: districtsError,
  } = useQuery({
    queryKey: ["districts"],
    queryFn: () => queries.listDistricts({ region: formData.region }).queryFn(),
    enabled: !!formData.region && formData.role === "nss_officer",
    onSuccess: (data) => {
      if (data?.data) {
        setDistricts(data.data);
      }
    },
    onError: (error) => {
      console.error("Error fetching districts:", error);
      toast.error("Failed to load districts for the selected region");
    },
  });

  // Function to get available districts (similar to add-center page)
  const getAvailableDistricts = () => {
    if (!districtsData?.data || !Array.isArray(districtsData.data)) return [];
    return districtsData.data.map((district) => ({
      value: district.id,
      label: district.districtName || district.name,
    }));
  };

  // Handle district selection change
  const handleDistrictChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      district: selectedOption ? selectedOption.value : null,
    }));
  };

  // Define columns based on the active filter
  const columns = useMemo(() => {
    const baseColumns = [
      {
        id: "role",
        accessorKey: "role",
        header: "Role",
        filterType: "select",
        cell: (info) => {
          const role = info.getValue();
          // Format role for display (capitalize, replace underscores with spaces)
          return role
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
        },
      },
      {
        id: "fullName",
        accessorKey: "fullName",
        header: "Name",
      },
      {
        id: "email",
        accessorKey: "email",
        header: "Email",
      },
      {
        id: "phoneNumber",
        accessorKey: "phoneNumber",
        header: "Phone Number",
      },
      {
        id: "status",
        accessorKey: "archivedDate",
        header: "Status",
        filterType: "select",
        cell: (info) => {
          const archivedDate = info.getValue();
          let displayStatus;
          let color;

          if (archivedDate) {
            displayStatus = "Disabled";
            color = "#ef4444"; // red
          } else {
            displayStatus = "Active";
            color = "#16a34a"; // green
          }

          return (
            <span
              style={{
                color: color,
                fontWeight: 500,
                padding: "0.25rem 0.5rem",
                borderRadius: "0.25rem",
                backgroundColor: `${color}15`,
              }}
            >
              {displayStatus}
            </span>
          );
        },
      },
    ];

    // Only add the region column for regional filter
    if (
      activeFilter === "regional" ||
      activeFilter === "regionalDirector" ||
      activeFilter === "nssOfficers"
    ) {
      // Insert region column before status
      baseColumns.splice(4, 0, {
        id: "region",
        accessorKey: "region",
        header: "Region",
        filterType: "select",
        cell: (info) => {
          const region = info.getValue();
          return region || "N/A";
        },
      });
    }

    return baseColumns;
  }, [activeFilter]);

  const filteredData = useMemo(() => {
    // If we don't have data yet, return an empty array
    if (!administratorsData?.data) return [];

    // For regional administrators, only show relevant data based on filter
    if (currentUserRole === "regional_administrator") {
      switch (activeFilter) {
        // case "regional":
        //   return administratorsData.data.filter(
        //     (admin) => admin.role === "regional_administrator"
        //   );
        case "regionalDirector":
          return administratorsData.data.filter(
            (admin) => admin.role === "regional_director"
          );
        case "nssOfficers":
          return administratorsData.data.filter(
            (admin) => admin.role === "nss_officer"
          );
        default:
          return administratorsData.data.filter(
            (admin) => admin.role === "regional_director"
          );
      }
    } else {
      // Original logic for non-regional administrators
      if (activeFilter === "national") {
        return administratorsData.data.filter(
          (admin) =>
            admin.role !== "regional_administrator" &&
            admin.role !== "regional_director" &&
            admin.role !== "nss_officer"
        );
      }
      if (activeFilter === "regional") {
        return administratorsData.data.filter(
          (admin) =>
            admin.role === "regional_administrator" ||
            admin.role === "regional_director" ||
            admin.role === "nss_officer"
        );
      }
      return administratorsData.data;
    }
  }, [activeFilter, administratorsData?.data, currentUserRole]);

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const administrator = {
      role: formData.role,
      fullName: formData.fullName,
      email: formData.email,
      phoneNumber: formData.phoneNumber,
      region:
        formData.role === "regional_administrator" ||
        formData.role === "regional_director" ||
        formData.role === "nss_officer"
          ? formData.region
          : null,
      district: formData.role === "nss_officer" ? formData.district : null,
    };

    addAdministratorMutation.mutate(administrator);
  };

  //   Roles and Users Management
  return (
    <Container>
      <TableWrapperContainer>
        <TableHeader>
          <HeaderTopRow>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />{" "}
              <PageTitle>Uploaded Entries</PageTitle>
            </TitleWrapper>
            <AddUserButton onClick={() => setShowAddUserModal(true)}>
              Add New User
            </AddUserButton>
          </HeaderTopRow>

          <ButtonGroup>
            {currentUserRole === "regional_administrator" ? (
              // Show these tabs only for regional administrators
              <>
                <FilterButton
                  $active={activeFilter === "regional"}
                  onClick={() => setActiveFilter("regional")}
                >
                  Regional
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "regionalDirector"}
                  onClick={() => setActiveFilter("regionalDirector")}
                >
                  Regional Director
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "nssOfficers"}
                  onClick={() => setActiveFilter("nssOfficers")}
                >
                  NSS Officers
                </FilterButton>
              </>
            ) : (
              // Show original tabs for other users
              <>
                <FilterButton
                  $active={activeFilter === "national"}
                  onClick={() => setActiveFilter("national")}
                >
                  National
                </FilterButton>
                <FilterButton
                  $active={activeFilter === "regional"}
                  onClick={() => setActiveFilter("regional")}
                >
                  Regional
                </FilterButton>
              </>
            )}
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading administrators...</LoaderText>
            </LoaderContainer>
          ) : error ? (
            <div
              style={{
                color: "#ef4444",
                padding: "1rem",
                textAlign: "center",
              }}
            >
              Error loading administrators: {error.message}
            </div>
          ) : administratorsData?.data?.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <Users size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No administrators found</EmptyStateText>
            </EmptyState>
          ) : filteredData.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <Users size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>
                {activeFilter === "national"
                  ? "No national administrators found"
                  : "No regional administrators found"}
              </EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              columns={columns}
              data={filteredData}
              pagination
              highlightOnHover
              responsive
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
              onRowClick={(row) => {
                setOpenPeakDrawer(false);
                setSelectedAdministrator(row);
                setOpenPeakDrawer(true);
              }}
            />
          )}
        </TableContent>
      </TableWrapperContainer>

      <PeakDetails
        administrator={selectedAdministrator}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />

      {showAddUserModal && (
        <AddUserModal>
          <AddUserModalContent>
            <AddUserModalHeader>
              <AddUserModalTitle>Add New User</AddUserModalTitle>
              <CloseButton
                onClick={() => {
                  setShowAddUserModal(false);
                  resetForm();
                }}
              >
                <X size={18} />
              </CloseButton>
            </AddUserModalHeader>
            <FormContainer onSubmit={handleSubmit}>
              <FormGroup fullWidth>
                <FormLabel>Role</FormLabel>
                <InputWrapper>
                  <IconWrapper>
                    <Shield size={16} />
                  </IconWrapper>
                  <FormSelect
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Role</option>
                    <option value="director_general">Director General</option>
                    <option value="regional_administrator">
                      Regional Administrator
                    </option>
                    <option value="deployment_officer">
                      Deployment Officer
                    </option>
                    <option value="head_of_deployment">
                      Head of Deployment
                    </option>
                    <option value="regional_director">Regional Director</option>
                    <option value="nss_officer">NSS Officer</option>
                  </FormSelect>
                </InputWrapper>
              </FormGroup>

              {(formData.role === "regional_administrator" ||
                formData.role === "regional_director" ||
                formData.role === "nss_officer") && (
                <FormGroup fullWidth>
                  <FormLabel>Region</FormLabel>
                  <InputWrapper>
                    <IconWrapper>
                      <Globe size={16} />
                    </IconWrapper>
                    <FormSelect
                      name="region"
                      value={formData.region}
                      onChange={handleInputChange}
                      required={
                        formData.role === "regional_administrator" ||
                        formData.role === "regional_director" ||
                        formData.role === "nss_officer"
                      }
                    >
                      <option value="">Select a region</option>
                      <option value="Greater Accra Region">
                        Greater Accra Region
                      </option>
                      <option value="Ashanti Region">Ashanti Region</option>
                      <option value="Western Region">Western Region</option>
                      <option value="Eastern Region">Eastern Region</option>
                      <option value="Central Region">Central Region</option>
                      <option value="Volta Region">Volta Region</option>
                      <option value="Northern Region">Northern Region</option>
                      <option value="Upper East Region">
                        Upper East Region
                      </option>
                      <option value="Upper West Region">
                        Upper West Region
                      </option>
                      <option value="North East Region">
                        North East Region
                      </option>
                      <option value="Savannah Region">Savannah Region</option>
                      <option value="Bono Region">Bono Region</option>
                      <option value="Bono East Region">Bono East Region</option>
                      <option value="Ahafo Region">Ahafo Region</option>
                      <option value="Western North Region">
                        Western North Region
                      </option>
                      <option value="Oti Region">Oti Region</option>
                    </FormSelect>
                  </InputWrapper>
                </FormGroup>
              )}

              {formData.role === "nss_officer" && (
                <FormGroup fullWidth>
                  <FormLabel>District</FormLabel>
                  <InputWrapper>
                    <IconWrapper>
                      <MapPin size={16} />
                    </IconWrapper>
                    <StyledSelect
                      classNamePrefix="react-select"
                      value={
                        formData.district
                          ? getAvailableDistricts().find(
                              (option) => option.value === formData.district
                            )
                          : null
                      }
                      onChange={handleDistrictChange}
                      options={getAvailableDistricts()}
                      isDisabled={!formData.region || isLoadingDistricts}
                      isLoading={isLoadingDistricts}
                      placeholder={
                        isLoadingDistricts
                          ? "Loading districts..."
                          : "Select or type to search"
                      }
                      isClearable
                      noOptionsMessage={() =>
                        formData.region
                          ? "No districts found for this region"
                          : "Please select a region first"
                      }
                    />
                  </InputWrapper>
                </FormGroup>
              )}

              <FormGroup>
                <FormLabel>Full Name</FormLabel>
                <InputWrapper>
                  <IconWrapper>
                    <User size={16} />
                  </IconWrapper>
                  <FormInput
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    placeholder="Enter full name"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <FormLabel>Email Address</FormLabel>
                <InputWrapper>
                  <IconWrapper>
                    <Mail size={16} />
                  </IconWrapper>
                  <FormInput
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter email address"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <FormGroup>
                <FormLabel>Phone Number</FormLabel>
                <InputWrapper>
                  <IconWrapper>
                    <Phone size={16} />
                  </IconWrapper>
                  <FormInput
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    placeholder="Enter phone number"
                    required
                  />
                </InputWrapper>
              </FormGroup>

              <ModalFooter>
                <Button
                  type="button"
                  $secondary
                  onClick={() => {
                    setShowAddUserModal(false);
                    resetForm();
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" $primary disabled={isSubmitting}>
                  {isSubmitting ? "Adding..." : "Add User"}
                </Button>
              </ModalFooter>
            </FormContainer>
          </AddUserModalContent>
        </AddUserModal>
      )}
    </Container>
  );
}
