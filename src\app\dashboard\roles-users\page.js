"use client";

import React from "react";
import { FileText } from "lucide-react";

// Components
import RolesUsersTable from "./components/RolesUsersTable";
import AddUserModal from "./components/AddUserModal";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";

// Hooks
import { useRolesUsers } from "./hooks/useRolesUsers";
import { useUserModal } from "./hooks/useUserModal";

// Styled Components
import {
  Container,
  TableWrapperContainer,
  TableHeader,
  HeaderTopRow,
  TitleWrapper,
  PageTitle,
  AddUserButton,
} from "./styles/RolesUsers.styled";

export default function RolesAndUsersPage() {
  // Custom hooks for state management
  const {
    currentUserRole,
    activeFilter,
    setActiveFilter,
    isLoading,
    error,
    columns,
    filteredData,
    selectedAdministrator,
    setSelectedAdministrator,
    openPeakDrawer,
    setOpenPeakDrawer,
  } = useRolesUsers();

  const {
    showAddUserModal,
    setShowAddUserModal,
    isSubmitting,
    formData,
    handleInputChange,
    handleSubmit,
    resetForm,
  } = useUserModal();

  return (
    <Container>
      <TableWrapperContainer>
        <TableHeader>
          <HeaderTopRow>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />
              <PageTitle>Roles & Users Management</PageTitle>
            </TitleWrapper>
            <AddUserButton onClick={() => setShowAddUserModal(true)}>
              Add New User
            </AddUserButton>
          </HeaderTopRow>

          <RolesUsersTable
            currentUserRole={currentUserRole}
            activeFilter={activeFilter}
            setActiveFilter={setActiveFilter}
            isLoading={isLoading}
            error={error}
            columns={columns}
            filteredData={filteredData}
            setSelectedAdministrator={setSelectedAdministrator}
            setOpenPeakDrawer={setOpenPeakDrawer}
          />
        </TableHeader>
      </TableWrapperContainer>

      <PeakDetails
        administrator={selectedAdministrator}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />

      <AddUserModal
        isOpen={showAddUserModal}
        onClose={() => {
          setShowAddUserModal(false);
          resetForm();
        }}
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
        formData={formData}
        handleInputChange={handleInputChange}
      />
    </Container>
  );
}
