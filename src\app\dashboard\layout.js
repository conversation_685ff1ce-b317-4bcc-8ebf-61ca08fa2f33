"use client";

import Sidebar from "@/components/dashboard/sidebar";
import TopBar from "@/components/dashboard/topbar";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import styled, { createGlobalStyle } from "styled-components";
import NextTopLoader from "nextjs-toploader"; // Add this import

// Global style to prevent body scrolling
const GlobalStyle = createGlobalStyle`
  body, html {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
`;

const LayoutContainer = styled.div`
  display: flex;
  height: 100vh;
  background-color: #119411;
  padding: 7px;
  overflow: hidden;
  box-sizing: border-box; /* Include padding in the height calculation */
`;

const MainContent = styled.div`
  flex-grow: 1;
  overflow-y: auto;
  background-color: #f9fafb;
  margin-left: 7px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
`;

const TopBarWrapper = styled.div`
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #f9fafb;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
`;

const ContentWrapper = styled.div`
  flex: 1;
  overflow-y: auto;
  /* padding: 20px; */
`;

const DashLayout = ({ children }) => {
  const pathname = usePathname();
  const [pageTitle, setPageTitle] = useState();

  useEffect(() => {
    const extractPageTitle = () => {
      // Try to find a PageTitle component in the DOM
      const pageTitleElement = document.querySelector("h1.page-title");
      if (pageTitleElement) {
        setPageTitle(pageTitleElement.textContent);
      } else {
        // Default titles based on pathname
        if (pathname.includes("/students/special-posting")) {
          setPageTitle("Special Posting Requests");
        } else if (pathname.includes("/students/time-tracking")) {
          setPageTitle("View Attendance History");
        } else if (pathname.includes("/batches")) {
          setPageTitle("All Enrollment on Record");
        } else if (pathname.includes("/students")) {
          setPageTitle("All Personnel on Record");
        } else if (pathname.includes("/institutions")) {
          setPageTitle("All Institutions on Record");
        } else if (pathname.includes("/companies")) {
          setPageTitle("User Agencies");
        } else if (pathname.includes("/settings")) {
          setPageTitle("Account Settings");
        } else if (pathname.includes("/notifications")) {
          setPageTitle("Notifications");
        } else if (pathname.includes("/roles-users")) {
          setPageTitle("Roles & Users");
        } else if (pathname.includes("/districts")) {
          setPageTitle("Manage Centers");
        } else {
          setPageTitle("Dashboard Overview");
        }
      }
    };

    const timer = setTimeout(extractPageTitle, 100);
    return () => clearTimeout(timer);
  }, [pathname, children]);

  const handleMenuItemClick = (itemId) => {
    console.log(`Menu item clicked: ${itemId}`);
    // Handle navigation or other actions
  };
  return (
    <>
      <GlobalStyle />
      <NextTopLoader color="#10b981" showSpinner={false} height={3} />
      <LayoutContainer>
        <Sidebar
          logo="/images/nsa-logo.png"
          userName="Barbara Amoakoa"
          userEmail="<EMAIL>"
          userImage="/logos/GovLogo.png"
          onMenuItemClick={handleMenuItemClick}
        />
        <MainContent>
          <TopBarWrapper>
            <TopBar pageTitle={pageTitle} />
          </TopBarWrapper>
          <ContentWrapper>{children}</ContentWrapper>
        </MainContent>
      </LayoutContainer>
    </>
  );
};

export default DashLayout;
