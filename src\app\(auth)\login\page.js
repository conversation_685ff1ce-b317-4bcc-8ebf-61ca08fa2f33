"use client";

import { useState } from "react";
import AuthForm from "@/components/form/form";
import { FiLock, FiUser } from "react-icons/fi";
import { api } from "@/api/client";
import { useRouter } from "next/navigation";

const Login = () => {
  const [isLoadingLogin, setIsLoadingLogin] = useState(false);
  const router = useRouter();

  const loginFields = [
    {
      name: "email",
      type: "email",
      label: "User ID",
      placeholder: "Email",
      required: true,
      icon: <FiUser />,
    },
    {
      name: "password",
      type: "password",
      label: "Password",
      placeholder: "••••••••••",
      required: true,
      icon: <FiLock />,
    },
    { name: "keepLoggedIn", type: "checkbox", label: "Keep me logged in" },
  ];

  const links = {
    toggleText: "Don't have an account?",
    toggleLabel: "Register",
    toggleLink: "/register",
    forgotPassword: "/forgot-password",
    home: "/",
  };

  const handleLogin = async (formData) => {
    setIsLoadingLogin(true);

    try {
      const response = await api.loginPowerUser({
        email: formData.email,
        password: formData.password,
      });

      // Check if response exists and has data property
      if (response && response.data && response.data.token) {
        sessionStorage.setItem("authToken", response.data.token);
        sessionStorage.setItem("user", JSON.stringify(response.data.user));
        router.push("/dashboard");
      } else {
        console.error("Invalid response structure:", response);
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Login failed:", error);
      // Handle specific error cases if needed
      if (error.response?.status === 401) {
        // Handle unauthorized error
        console.error("Invalid credentials");
      }
      setIsLoadingLogin(false);
    }
  };

  return (
    <AuthForm
      title="Log into your account"
      subtitle="Enter your details to login"
      fields={loginFields}
      links={links}
      buttonText="Log In"
      onSubmit={handleLogin}
      pending={isLoadingLogin}
      disabled={isLoadingLogin}
    />
  );
};

export default Login;
