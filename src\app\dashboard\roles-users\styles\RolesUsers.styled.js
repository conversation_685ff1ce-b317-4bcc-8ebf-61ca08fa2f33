import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

export const PageTitle = styled.h1`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

export const AddUserButton = styled.button`
  background-color: #16a34a;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #15803d;
  }
`;

export const TableWrapperContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

export const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
`;

export const HeaderTopRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0.75rem;
`;

export const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

export const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

export const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => (props.$active ? "#16a34a" : "#6b7280")};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => (props.$active ? "#16a34a" : "transparent")};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: #16a34a;
  }
`;

export const TableContent = styled.div`
  /* Table content styling can be added here if needed */
`;

export const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

export const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

export const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

export const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

export const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

export const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;
