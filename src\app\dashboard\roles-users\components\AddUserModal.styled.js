import styled from "styled-components";
import Select from "react-select";

export const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

export const ModalContent = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  max-height: 90vh;
  overflow-y: auto;
`;

export const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

export const ModalTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

export const CloseButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #f3f4f6;
  }
`;

export const FormContainer = styled.form`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
  padding: 0 0.5rem;
`;

export const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  grid-column: ${(props) => (props.fullWidth ? "1 / span 2" : "auto")};
`;

export const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

export const InputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

export const IconWrapper = styled.div`
  position: absolute;
  left: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 1;
`;

export const FormInput = styled.input`
  padding: 0.625rem 0.625rem 0.625rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #111827;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 1px #16a34a;
  }
`;

export const FormSelect = styled.select`
  padding: 0.625rem 0.625rem 0.625rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  width: 100%;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 1px #16a34a;
  }
`;

export const StyledSelect = styled(Select)`
  width: 100%;
  font-size: 0.875rem;

  .react-select__control {
    border-color: #d1d5db;
    border-radius: 0.375rem;
    min-height: 2.5rem;
    box-shadow: none;
    padding-left: 2rem;
  }

  .react-select__control:hover {
    border-color: #9ca3af;
  }

  .react-select__control--is-focused {
    border-color: #16a34a !important;
    box-shadow: 0 0 0 1px #16a34a !important;
  }

  .react-select__placeholder {
    color: #9ca3af;
  }

  .react-select__indicator-separator {
    background-color: #d1d5db;
  }

  .react-select__menu {
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 100;
  }

  .react-select__option {
    padding: 0.5rem 1rem;
  }

  .react-select__option--is-focused {
    background-color: #f3f4f6;
  }

  .react-select__option--is-selected {
    background-color: #16a34a;
    color: white;
  }
`;

export const ModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  grid-column: 1 / span 2;
  margin-top: 1rem;
`;

export const Button = styled.button`
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  ${(props) =>
    props.$primary &&
    `
    background-color: #16a34a;
    color: white;
    border: none;
    
    &:hover {
      background-color: #15803d;
    }
    
    &:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }
  `}

  ${(props) =>
    props.$secondary &&
    `
    background-color: white;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background-color: #f9fafb;
    }
    
    &:disabled {
      background-color: #f3f4f6;
      color: #9ca3af;
      cursor: not-allowed;
    }
  `}
`;
