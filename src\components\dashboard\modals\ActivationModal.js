import styled from "styled-components";
import { useState } from "react";

// Add DatePicker styled component
const DatePickerContainer = styled.div`
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const DateInputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const DateLabel = styled.label`
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
`;

const DateInput = styled.input`
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #111827;
  
  &:focus {
    outline: none;
    border-color: #16a34a;
    box-shadow: 0 0 0 1px #16a34a;
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalTitle = styled.h3`
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
`;

const ModalText = styled.p`
  margin: 0 0 1.5rem 0;
  color: #6b7280;
`;

const ModalButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const ModalButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border: none;

  ${(props) =>
    props.variant === "confirm" &&
    `
    background-color: #16a34a;
    color: white;
    &:hover {
      background-color: #15803d;
    }
  `}

  ${(props) =>
    props.variant === "cancel" &&
    `
    background-color: #f3f4f6;
    color: #374151;
    &:hover {
      background-color: #e5e7eb;
    }
  `}
`;

const ActivationModal = ({ isOpen, onClose, onConfirm, isLoading, isExtendMode = false }) => {
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (isExtendMode) {
      onConfirm({ endDate });
    } else {
      onConfirm({ startDate, endDate });
    }
  };

  return (
    <ModalOverlay>
      <ModalContent>
        <ModalTitle>{isExtendMode ? "Extend Registration End Date" : "Activate Batch"}</ModalTitle>
        <ModalText>
          {isExtendMode 
            ? "Please select a new end date for the registration period."
            : "Please select the start and end dates for this batch before activation."}
        </ModalText>
        
        <DatePickerContainer>
          {!isExtendMode && (
            <DateInputGroup>
              <DateLabel>Start Date</DateLabel>
              <DateInput
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                required
              />
            </DateInputGroup>
          )}
          <DateInputGroup>
            <DateLabel>{isExtendMode ? "New End Date" : "End Date"}</DateLabel>
            <DateInput
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              min={isExtendMode ? new Date().toISOString().split('T')[0] : startDate}
              required
            />
          </DateInputGroup>
        </DatePickerContainer>

        <ModalButtons>
          <ModalButton variant="cancel" onClick={onClose}>
            Cancel
          </ModalButton>
          <ModalButton
            variant="confirm"
            onClick={handleConfirm}
            disabled={isLoading || (isExtendMode ? !endDate : (!startDate || !endDate))}
          >
            {isLoading 
              ? (isExtendMode ? "Extending..." : "Activating...") 
              : (isExtendMode ? "Extend" : "Activate")}
          </ModalButton>
        </ModalButtons>
      </ModalContent>
    </ModalOverlay>
  );
};

export default ActivationModal;