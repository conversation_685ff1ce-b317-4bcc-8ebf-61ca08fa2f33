"use client";

import React, { useState, useMemo, useEffect } from "react";
import styled from "styled-components";
import { DataTable } from "@globalicons/enterprise-tools";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { queries } from "@/api/client";
import {
  FileText,
  PlusCircle,
  Building,
  X,
  Mail,
  Phone,
  MapPin,
  Globe,
  MoreVertical,
} from "lucide-react";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const AddDistrictButton = styled.button`
  background-color: #16a34a;
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #15803d;
  }
`;

const TableWrapperContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1rem;
`;

const HeaderTopRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 0.75rem;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => (props.$active ? "#16a34a" : "#6b7280")};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => (props.$active ? "#16a34a" : "transparent")};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: #16a34a;
  }
`;

const TableContent = styled.div`
  /* Table content styling - DataTable handles its own internal spacing */
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

export default function DistrictManagementPage() {
  const router = useRouter();
  const { push } = useRouter();
  const [activeFilter, setActiveFilter] = useState("all");
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const [error, setError] = useState(null);

  const queryClient = useQueryClient();

  const {
    data: centersData,
    isLoading: isLoadingCenters,
    error: centersError,
  } = useQuery({
    ...queries.listCenters(),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
      setError(error);
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Effect for handling clicks outside dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  // Filter districts based on active filter
  const filteredDistricts = useMemo(() => {
    if (!centersData?.data) return [];

    const districts = centersData.data;

    switch (activeFilter) {
      case "active":
        return districts.filter((district) => district.status === "active");
      case "inactive":
        return districts.filter((district) => district.status === "inactive");
      default:
        return districts;
    }
  }, [activeFilter, centersData]);

  // Define columns for the DataTable
  const columns = [
    {
      id: "centerName",
      accessorKey: "centerName", // You'll need to add this property to your data
      header: "Center Name",
    },
    {
      id: "region",
      accessorKey: "region",
      header: "Region",
      filterType: "select",
    },
    {
      id: "district",
      accessorKey: "district",
      header: "District Name",
      cell: (info) => {
        const districts = info.getValue();
        return Array.isArray(districts)
          ? districts.join(", ")
          : districts || "";
      },
    },

    {
      id: "personnelPosted",
      accessorKey: "personnelPosted", // You'll need to add this property to your data
      header: "Personnel Posted",
      cell: (info) => {
        const count = info.getValue();
        return count || "0"; // Display 0 if no value is provided
      },
    },
    {
      id: "address",
      accessorKey: "ghanaPostGPS",
      header: "GPS Address",
    },

    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      filterType: "select",
      cell: (info) => {
        const status = info.getValue();
        // Changed this line to always display "Active" for now
        let displayStatus = "Active";
        let color = "#16a34a"; // Green color for active status

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {displayStatus}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // View details action
                    console.log("View details for:", info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Edit action
                    console.log("Edit district:", info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  Edit
                </button>
                {info.row.original.status === "active" ? (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Deactivate action
                      console.log("Deactivate district:", info.row.original);
                      setOpenDropdownId(null);
                    }}
                    style={{
                      display: "block",
                      width: "100%",
                      padding: "0.5rem 1rem",
                      textAlign: "left",
                      border: "none",
                      background: "none",
                      cursor: "pointer",
                      color: "#ef4444",
                    }}
                  >
                    Deactivate
                  </button>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // Activate action
                      console.log("Activate district:", info.row.original);
                      setOpenDropdownId(null);
                    }}
                    style={{
                      display: "block",
                      width: "100%",
                      padding: "0.5rem 1rem",
                      textAlign: "left",
                      border: "none",
                      background: "none",
                      cursor: "pointer",
                      color: "#16a34a",
                    }}
                  >
                    Activate
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  // Add CSS for dropdown
  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      .dropdown {
        position: relative;
        display: inline-block;
      }
      .dropdown:hover .dropdown-content {
        display: block;
      }
      .dropdown-content button:hover {
        background-color: #f3f4f6;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  return (
    <Container>
      <TableWrapperContainer>
        <TableHeader>
          <HeaderTopRow>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />
              <TableTitle>Uploaded Entries</TableTitle>
            </TitleWrapper>
            <AddDistrictButton
              onClick={() => router.push("/dashboard/districts/add-center")}
            >
              Add District Center
            </AddDistrictButton>
          </HeaderTopRow>

          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "all"}
              onClick={() => setActiveFilter("all")}
            >
              All
            </FilterButton>
            <FilterButton
              $active={activeFilter === "active"}
              onClick={() => setActiveFilter("active")}
            >
              Active
            </FilterButton>
            <FilterButton
              $active={activeFilter === "inactive"}
              onClick={() => setActiveFilter("inactive")}
            >
              Inactive
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {isLoadingCenters ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading centers...</LoaderText>
            </LoaderContainer>
          ) : filteredDistricts.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <Building size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No district centers found</EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              columns={columns}
              data={filteredDistricts}
              pagination
              highlightOnHover
              responsive
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
              onRowClick={(row) => {
                console.log("Row clicked:", row);
                // Handle row click, e.g., navigate to details page
                // push(`/dashboard/districts/${row.id}/details`);
              }}
            />
          )}
        </TableContent>
      </TableWrapperContainer>
    </Container>
  );
}
