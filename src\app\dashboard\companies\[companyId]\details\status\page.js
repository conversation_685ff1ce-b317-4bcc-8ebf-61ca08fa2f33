"use client";
import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";

export default function Status() {
  const params = useParams();
  const { push } = useRouter();
  const companyId = params.companyId;

  const { data: companyData } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  const company = companyData?.data;
  return (
    <MainContent>
      <SectionHeader>PROFILE</SectionHeader>

      <InstitutionHeaderSection>
        <InstitutionLogo>
          <Image
            src="/profile-pic-placeholder.svg"
            alt="Company Logo"
            width={80}
            height={80}
            style={{ objectFit: "contain" }}
          />
        </InstitutionLogo>
        <InstitutionHeaderInfo>
          <InstitutionLabel>Company Name</InstitutionLabel>
          <InstitutionName>
            {company?.name || "Global Tech Solutions"}
          </InstitutionName>
        </InstitutionHeaderInfo>
      </InstitutionHeaderSection>

      <SectionHeader>STATUS PROCESS</SectionHeader>
      <StatusContainer>
        <StatusTimeline>
          {/* Registration Step */}
          <StatusItem completed={true}>
            <StatusCircle completed={true} />
            <StatusContent>
              <StatusTitle>Registration</StatusTitle>
              <StatusDate>
                {company?.registrationDate || "January 15, 2023"}
              </StatusDate>
              <StatusDescription>
                Company successfully registered in the system
              </StatusDescription>
            </StatusContent>
          </StatusItem>

          {/* Document Verification Step */}
          <StatusItem completed={true}>
            <StatusCircle completed={true} />
            <StatusContent>
              <StatusTitle>Document Verification</StatusTitle>
              <StatusDate>
                {company?.verificationDate || "January 20, 2023"}
              </StatusDate>
              <StatusDescription>
                All required documents have been verified
              </StatusDescription>
            </StatusContent>
          </StatusItem>

          {/* Approval Step */}
          <StatusItem completed={company?.approved === true}>
            <StatusCircle completed={company?.approved === true} />
            <StatusContent>
              <StatusTitle>Approval</StatusTitle>
              <StatusDate>
                {company?.approvalDate ||
                  (company?.approved === true ? "February 5, 2023" : "Pending")}
              </StatusDate>
              <StatusDescription>
                {company?.approved === true
                  ? "Company has been approved"
                  : "Waiting for final approval"}
              </StatusDescription>
            </StatusContent>
          </StatusItem>

          {/* Onboarding Step */}
          <StatusItem completed={company?.onboarded === true}>
            <StatusCircle completed={company?.onboarded === true} />
            <StatusContent>
              <StatusTitle>Onboarding</StatusTitle>
              <StatusDate>
                {company?.onboardingDate ||
                  (company?.onboarded === true
                    ? "February 15, 2023"
                    : "Pending")}
              </StatusDate>
              <StatusDescription>
                {company?.onboarded === true
                  ? "Company has completed onboarding"
                  : "Onboarding process not started yet"}
              </StatusDescription>
            </StatusContent>
          </StatusItem>

          {/* Active Status Step */}
          <StatusItem completed={company?.active === true} isLast={true}>
            <StatusCircle completed={company?.active === true} />
            <StatusContent>
              <StatusTitle>Active Status</StatusTitle>
              <StatusDate>
                {company?.activeDate ||
                  (company?.active === true ? "March 1, 2023" : "Pending")}
              </StatusDate>
              <StatusDescription>
                {company?.active === true
                  ? "Company is now active in the system"
                  : "Company not yet active"}
              </StatusDescription>
            </StatusContent>
          </StatusItem>
        </StatusTimeline>
      </StatusContainer>
    </MainContent>
  );
}

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const StatusContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  margin: 1.5rem 0;
`;

const StatusTimeline = styled.div`
  position: relative;
  padding: 1rem 0;
`;

const StatusItem = styled.div`
  display: flex;
  position: relative;
  padding-bottom: ${(props) => (props.isLast ? "0" : "3rem")};

  &::after {
    content: "";
    position: absolute;
    left: 6.5px;
    top: 24px;
    bottom: ${(props) => (props.isLast ? "0" : "0")};
    width: 1px;
    height: 50px;
    background-color: ${(props) => (props.completed ? "#119411" : "#e5e7eb")};
    display: ${(props) => (props.isLast ? "none" : "block")};
  }
`;

const StatusCircle = styled.div`
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: ${(props) => (props.completed ? "#119411" : "#f3f4f6")};
  border: 2px solid ${(props) => (props.completed ? "#119411" : "#e5e7eb")};
  margin-right: 1.5rem;
  z-index: 1;
  flex-shrink: 0;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background-color: ${(props) =>
      props.completed ? "rgba(17, 148, 17, 0.15)" : "rgba(229, 231, 235, 0.5)"};
    z-index: -1;
  }
`;

const StatusContent = styled.div`
  flex: 1;
`;

const StatusTitle = styled.h3`
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
`;

const StatusDate = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const StatusDescription = styled.p`
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
`;
