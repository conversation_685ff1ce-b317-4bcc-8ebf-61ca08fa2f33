// src/components/DataTable/Pagination.js
import React from "react";
import styled, { css } from "styled-components";

// Styled Components
const PaginationContainer = styled.div`
  display: flex;
  align-items: center;
`;

const PageInfo = styled.span`
  font-size: 0.875rem;
  color: #4b5563;
  margin-right: 1rem;
`;

const PaginationControls = styled.div`
  display: flex;
`;

const PageButton = styled.button`
  all: unset;
  
  padding: 0.25rem 0.75rem;
  margin: 0 0.25rem;
  border-radius: 0.25rem;
  color: ${(props) => (props.disabled ? "#d1d5db" : "#374151")};
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};

  ${(props) =>
    props.$active &&
    css`
      background-color: #2563eb;
      color: white;
    `}

  ${(props) =>
    !props.$active &&
    !props.disabled &&
    css`
      &:hover {
        background-color: #f3f4f6;
      }
    `}
`;

/**
 * Pagination - Controls for navigating between pages of data
 * @param {Object} props - Component props
 * @param {Number} props.currentPage - Current active page number
 * @param {Number} props.totalPages - Total number of pages
 * @param {Function} props.onPageChange - Function to call when page is changed
 */
const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  // Generate array of page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // If total pages are less than max to show, display all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate middle pages to show
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're near the start or end
      if (currentPage <= 3) {
        endPage = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 2) {
        startPage = Math.max(2, totalPages - 3);
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push("...");
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pages.push("...");
      }

      // Always show last page
      pages.push(totalPages);
    }

    return pages;
  };

  return (
    <PaginationContainer>
      <PageInfo>
        {currentPage} / {totalPages} Page
      </PageInfo>

      <PaginationControls>
        {/* Previous page button */}
        <PageButton
          onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <svg
            style={{ width: "1.25rem", height: "1.25rem" }}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </PageButton>

        {/* Page number buttons */}
        {getPageNumbers().map((page, idx) => (
          <PageButton
            key={idx}
            onClick={() => page !== "..." && onPageChange(page)}
            $active={page === currentPage}
            disabled={page === "..."}
          >
            {page}
          </PageButton>
        ))}

        {/* Next page button */}
        <PageButton
          onClick={() =>
            currentPage < totalPages && onPageChange(currentPage + 1)
          }
          disabled={currentPage === totalPages}
        >
          <svg
            style={{ width: "1.25rem", height: "1.25rem" }}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </PageButton>
      </PaginationControls>
    </PaginationContainer>
  );
};

export default Pagination;
