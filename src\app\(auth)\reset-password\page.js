"use client";
import AuthForm from "@/components/form/form";
import { FiMail } from "react-icons/fi";

const ResetPassword = () => {
  const forgotFields = [
    {
      name: "email",
      type: "email",
      label: "Administrator Email",
      placeholder: "Enter your email",
      required: true,
      icon: <FiMail />,
    },
  ];

  const links = {
    toggleText: "Remember your password?",
    toggleLabel: "Log In",
    toggleLink: "/login",
    home: "/",
  };

  const handleForgotPassword = (formData) => {
    console.log("Forgot Password request:", formData);
  };

  return (
    <AuthForm
      title="Reset Your Password"
      subtitle="Enter your email to receive reset instructions"
      fields={forgotFields}
      links={links}
      buttonText="Reset Password"
      onSubmit={handleForgotPassword}
      bottomLinkText="Try another account" // Change text
      bottomLinkHref="/login" // Change link
    />
  );
};

export default ResetPassword;
