"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { useRouter } from "next/navigation";
import { ArrowLeft, ChevronLeft, Plus, Trash2, Loader } from "lucide-react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import Select from "react-select";
import { api, mutations, queries } from "@/api/client";
import toast from "react-hot-toast";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: #f9fafb;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;

  &:hover {
    color: #111827;
  }
`;

const FormContainer = styled.div`
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const FormTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  font-weight: 500;
  color: #374151;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;

  &:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
`;

const SelectRegion = styled.select`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  background-color: white;

  &:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
`;

const StyledSelect = styled(Select)`
  .react-select__control {
    border-color: #d1d5db;
    border-radius: 0.375rem;
    min-height: 42px;
  }
  .react-select__control:hover {
    border-color: #10b981;
  }
  .react-select__control--is-focused {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    border-color: #10b981;
  }
  .react-select__menu {
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  .react-select__option--is-focused {
    background-color: #f3f4f6;
  }
  .react-select__option--is-selected {
    background-color: #10b981;
  }
`;

const DistrictSection = styled.div`
  margin-top: 2rem;
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
`;

const DistrictHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const DistrictTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
`;

const AddDistrictButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #e5e7eb;
  }
`;

const DistrictCard = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background-color: #f9fafb;
  position: relative;
`;

const RemoveButton = styled.button`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;

  &:hover {
    color: #dc2626;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const SaveButton = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #16a34a;
  color: white;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    background-color: #15803d;
  }
`;

export default function AddDistrictCenterPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [centerData, setCenterData] = useState({
    name: "",
    districtName: "",
    region: "",
    gpsAddress: "",
  });
  const [districts, setDistricts] = useState([{ id: 1, district: "" }]);

  // Fetch districts based on selected region using the existing listDistricts API
  const {
    data: districtsData,
    isLoading: isLoadingDistricts,
    error: districtsError,
  } = useQuery({
    queryKey: ["districts", centerData.region, centerData.districtName],
    queryFn: () =>
      api.listDistricts(centerData.region ? { region: centerData.region } : {}),
    enabled: !!centerData.region,
    onError: (error) => {
      console.error("Error fetching districts:", error);
      toast.error("Failed to load districts for the selected region");
    },
  });

  // Set up the mutation
  const { mutate: addCenter, isPending } = useMutation({
    ...mutations.addCenter,
    onSuccess: () => {
      // Invalidate and refetch the districts list
      queryClient.invalidateQueries({ queryKey: ["centers"] });
      router.push("/dashboard/districts");
    },
    onError: (error) => {
      console.error("Error adding district center:", error);
      toast.error(
        error?.response?.data?.message || "Failed to add district center"
      );
      setIsSubmitting(false);
    },
  });

  const regions = [
    "Greater Accra Region",
    "Ashanti Region",
    "Western Region",
    "Eastern Region",
    "Central Region",
    "Volta Region",
    "Northern Region",
    "Upper East Region",
    "Upper West Region",
    "North East Region",
    "Savannah Region",
    "Bono Region",
    "Bono East Region",
    "Ahafo Region",
    "Western North Region",
    "Oti Region",
  ];

  // Function to get available districts (filtering out already selected ones)
  const getAvailableDistricts = () => {
    if (!districtsData?.data || !Array.isArray(districtsData.data)) return [];

    // Get all districts for the selected region
    const allDistricts = districtsData.data;

    // Get already selected districts
    const selectedDistrictIds = districts
      .map((d) => d.district)
      .filter(Boolean);

    // Return only districts that haven't been selected yet
    return allDistricts.filter(
      (district) => !selectedDistrictIds.includes(district.id)
    );
  };

  const handleCenterDataChange = (e) => {
    const { name, value } = e.target;
    setCenterData({
      ...centerData,
      [name]: value,
    });

    // If region changed, reset district selections
    if (name === "region") {
      setDistricts([{ id: 1, district: "" }]);
    }
  };

  const handleDistrictChange = (id, selectedOption) => {
    setDistricts(
      districts.map((district) =>
        district.id === id
          ? {
              ...district,
              district: selectedOption ? selectedOption.value : null,
            }
          : district
      )
    );
  };

  const addDistrict = () => {
    const newId = Math.max(...districts.map((d) => d.id), 0) + 1;
    setDistricts([...districts, { id: newId, district: "" }]);
  };

  const removeDistrict = (id) => {
    if (districts.length > 1) {
      setDistricts(districts.filter((district) => district.id !== id));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validate form data
    if (
      !centerData.name ||
      !centerData.region ||
      districts.some((d) => !d.district)
    ) {
      toast.error("Please fill in all required fields");
      setIsSubmitting(false);
      return;
    }

    // Format the data to match the API's expected structure
    const formData = {
      centerName: centerData.name,
      region: centerData.region,
      ghanaPostGPS: centerData.gpsAddress || null,
      districtIds: districts.map((d) => d.district), // Using the district ID
    };

    // Call the API
    addCenter(formData);
  };

  const handleCancel = () => {
    router.push("/dashboard/districts");
  };

  // Get available districts for the current region
  const availableDistricts = getAvailableDistricts();

  return (
    <Container>
      <BackButton onClick={() => router.back()}>
        <ChevronLeft size={16} />
        Back
      </BackButton>

      <FormContainer>
        <FormTitle>Add New District Center</FormTitle>

        <form onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="centerName">Center Name</Label>
            <Input
              id="centerName"
              name="name"
              type="text"
              value={centerData.name}
              onChange={handleCenterDataChange}
              placeholder="Enter center name"
              required
              disabled={isPending}
            />
          </FormGroup>

          <FormGroup>
            <Label htmlFor="centerRegion">Region</Label>
            <SelectRegion
              id="centerRegion"
              name="region"
              value={centerData.region}
              onChange={handleCenterDataChange}
              required
              disabled={isPending}
            >
              <option value="">Select Region</option>
              {regions.map((region) => (
                <option key={region} value={region}>
                  {region}
                </option>
              ))}
            </SelectRegion>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="gpsAddress">GPS Address</Label>
            <Input
              id="gpsAddress"
              name="gpsAddress"
              type="text"
              value={centerData.gpsAddress}
              onChange={handleCenterDataChange}
              placeholder="Enter GPS address"
              disabled={isPending}
            />
          </FormGroup>

          <DistrictSection>
            <DistrictHeader>
              <DistrictTitle>Districts</DistrictTitle>
              <AddDistrictButton
                type="button"
                onClick={addDistrict}
                disabled={
                  isPending ||
                  !centerData.region ||
                  isLoadingDistricts ||
                  availableDistricts.length === 0
                }
              >
                <Plus size={16} />
                Add Another District
              </AddDistrictButton>
            </DistrictHeader>

            {districts.map((district) => (
              <DistrictCard key={district.id}>
                {/* ... RemoveButton */}
                <FormGroup>
                  <Label htmlFor={`district-${district.id}`}>District</Label>
                  <StyledSelect
                    id={`district-${district.id}`}
                    value={
                      districtsData?.data
                        ? districtsData.data
                            .filter((d) => d.id === district.district)
                            .map((d) => ({
                              value: d.id,
                              label: d.districtName,
                            }))[0]
                        : null
                    }
                    onChange={(selectedOption) =>
                      handleDistrictChange(district.id, selectedOption)
                    }
                    options={
                      districtsData?.data?.map((d) => ({
                        value: d.id,
                        label: d.districtName,
                      })) || []
                    }
                    isDisabled={
                      isPending || !centerData.region || isLoadingDistricts
                    }
                    isLoading={isLoadingDistricts}
                    placeholder={
                      isLoadingDistricts
                        ? "Loading districts..."
                        : "Select or type to search"
                    }
                    noOptionsMessage={() => "No districts found"}
                    isClearable
                  />
                </FormGroup>
              </DistrictCard>
            ))}
          </DistrictSection>

          <ButtonGroup>
            <CancelButton
              type="button"
              onClick={handleCancel}
              disabled={isPending}
            >
              Cancel
            </CancelButton>
            <SaveButton type="submit" disabled={isPending}>
              {isPending ? <>Saving...</> : "Save Changes"}
            </SaveButton>
          </ButtonGroup>
        </form>
      </FormContainer>
    </Container>
  );
}

// Add a new styled component for the loading indicator
const LoadingIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
`;
