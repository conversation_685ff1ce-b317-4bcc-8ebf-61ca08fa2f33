import React from "react";
import styled from "styled-components";
import { useRouter } from "next/navigation";

const Card = styled.div`
  background-color: #f5f7fa;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  cursor: pointer;
  transition: box-shadow 0.15s;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const CardTitle = styled.h3`
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
`;

const CardSubtitle = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
  text-transform: uppercase;
  margin: 0.5rem 0;
`;

const ChevronButton = styled.button`
  color: #9ca3af;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #4b5563;
    background-color: #f3f4f6;
  }
`;

const CardContent = styled.div`
  font-size: 1rem;
  color: #4b5563;
`;

const InfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const DateLabel = styled.p`
  margin: 0;
  font-weight: 500;
  font-size: 0.875rem;
`;

const formatDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
  });
};

const formatCreatedDate = (dateString) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "2-digit",
  });
};

const BatchCard = ({
  title,
  created,
  startDate,
  endDate,
  onClick,
  stage,
  institutions,
  id,
}) => {
  const router = useRouter();

  const handleChevronClick = (e) => {
    e.stopPropagation();
    router.push(`/dashboard/batches/${id}/details`);
  };

  return (
    <Card onClick={onClick}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <ChevronButton onClick={handleChevronClick}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </ChevronButton>
      </CardHeader>

      <CardSubtitle>Created: {formatCreatedDate(created)}</CardSubtitle>
      <CardSubtitle>Type: {institutions}</CardSubtitle>
      <CardSubtitle>Stage: {stage}</CardSubtitle>

      <CardContent>
        <InfoContainer>
          <DateLabel>Start: {formatDate(startDate)}</DateLabel>
          <DateLabel>End: {formatDate(endDate)}</DateLabel>
        </InfoContainer>
      </CardContent>
    </Card>
  );
};

export default BatchCard;
