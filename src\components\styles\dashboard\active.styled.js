import styled from "styled-components";

// Styled Components
export const Container = styled.div`
  max-width: 42rem;
`;

export const Title = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
`;

export const Description = styled.p`
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 2rem;
`;

export const ErrorMessage = styled.div`
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #fef2f2;
  color: #b91c1c;
  border-radius: 0.5rem;
`;

export const SessionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

export const SessionCard = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
`;

export const SessionInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

export const IconWrapper = styled.div`
  width: 2.5rem;
  height: 2.5rem;
  background-color: #f3f4f6;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #4b5563;
  }
`;

export const DeviceInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

export const DeviceName = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;

  span:first-child {
    font-weight: 500;
  }

  span:last-child {
    color: #6b7280;
    font-size: 0.875rem;
  }
`;

export const Role = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
`;

// export const CloseButton = styled.button`
//   color: #9ca3af;
// `;

export const SignOutButton = styled.button`
  margin-top: 2rem;
  padding: 0.75rem 1.5rem;
  color: #ef4444;
  border: 1px solid #fca5a5;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  width: 100%;

  @media (min-width: 768px) {
    width: auto;
  }

  &:hover {
    background-color: #fef2f2;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export const EmptyMessage = styled.p`
  margin-top: 2rem;
  text-align: center;
  color: #6b7280;
`;
