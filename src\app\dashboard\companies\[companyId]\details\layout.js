"use client";

import { usePara<PERSON>, useRouter, usePathname } from "next/navigation";
import styled from "styled-components";
import { ChevronLeft, Download, MoreVertical, Printer } from "lucide-react";
import { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queries, api } from "@/api/client";
import { useRef } from "react";

export default function CompanyLayout({ children }) {
  const params = useParams();
  const { push } = useRouter();
  const pathname = usePathname();
  const companyId = params.companyId;

  // Determine active tab based on current path
  const getInitialActiveTab = () => {
    if (pathname.includes("/request-personnel")) return "Request Personnel";
    if (pathname.includes("/status")) return "Status";
    if (pathname.includes("/notes")) return "Notes";
    if (pathname.includes("/postings")) return "Postings";
    return "Company Overview";
  };

  const [activeTab, setActiveTab] = useState(getInitialActiveTab());
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const queryClient = useQueryClient();
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  const { mutate: approveCompany, isLoading: isApproveLoading } = useMutation({
    mutationFn: () => api.approveCompany({ companyId, approved: true }),
    onSuccess: () => {
      // Invalidate and refetch the company data
      queryClient.invalidateQueries(["company", companyId]);
      setIsApproving(false);
    },
    onError: (error) => {
      console.error("Error approving company:", error);
      setIsApproving(false);
    },
  });

  // Add the reject company mutation
  const { mutate: rejectCompany, isLoading: isRejectLoading } = useMutation({
    mutationFn: () => api.approveCompany({ companyId, approved: false }),
    onSuccess: () => {
      // Invalidate and refetch the company data
      queryClient.invalidateQueries(["company", companyId]);
      setIsRejecting(false);
    },
    onError: (error) => {
      console.error("Error rejecting company:", error);
      setIsRejecting(false);
    },
  });

  // Handle approve button click
  const handleApprove = () => {
    setIsApproving(true);
    approveCompany();
  };

  // Handle reject button click
  const handleReject = () => {
    setIsRejecting(true);
    setShowRejectModal(true);
    rejectCompany();
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Add company data query
  const {
    data: companyData,
    isLoading,
    error,
  } = useQuery({
    ...queries.getCompany(companyId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  const company = companyData?.data;

  if (isLoading) {
    return (
      <Container>
        <LoaderContainer>
          <LoaderSpinner />
          <LoaderText>Loading company details...</LoaderText>
        </LoaderContainer>
      </Container>
    );
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  // Now this will work properly
  const needsApproval = company?.approved === false;

  return (
    <Container>
      <ActionContainer>
        <PageTitle>View Company Details</PageTitle>
        <ButtonGroup>
          {needsApproval && (
            <>
              <ActionButton
                variant="approve"
                onClick={handleApprove}
                disabled={isApproveLoading || isApproving}
              >
                {isApproveLoading || isApproving ? "Approving..." : "Approve"}
              </ActionButton>
              <ActionButton
                variant="reject"
                onClick={() => setShowRejectModal(true)}
                disabled={isRejectLoading || isRejecting}
              >
                {isRejectLoading || isRejecting ? "Rejecting..." : "Reject"}
              </ActionButton>
            </>
          )}
          <DropdownContainer ref={dropdownRef}>
            <ActionButton
              variant="more"
              onClick={(e) => {
                e.stopPropagation();
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <MoreVertical size={16} />
            </ActionButton>
            {dropdownOpen && (
              <DropdownMenu>
                <DropdownItem
                  onClick={() => {
                    setDropdownOpen(false);
                  }}
                >
                  <Download size={16} />
                  Download as PDF
                </DropdownItem>
                <DropdownItem
                  onClick={() => {
                    window.print();
                    setDropdownOpen(false);
                  }}
                >
                  <Printer size={16} />
                  Print Page
                </DropdownItem>
              </DropdownMenu>
            )}
          </DropdownContainer>
        </ButtonGroup>
      </ActionContainer>
      <BackButtonContainer>
        <BackButton onClick={() => push("/dashboard/companies")}>
          <ChevronLeft size={16} />
          Back
        </BackButton>
      </BackButtonContainer>

      {/* Navigation Tabs */}
      <TabsContainer>
        <TabContent>
          <TabItem
            active={activeTab === "Company Overview"}
            onClick={() => {
              setActiveTab("Company Overview");
              push(`/dashboard/companies/${params.companyId}/details`);
            }}
          >
            Company Overview
          </TabItem>
          <TabItem
            active={activeTab === "Request Personnel"}
            onClick={() => {
              setActiveTab("Request Personnel");
              push(
                `/dashboard/companies/${params.companyId}/details/request-personnel`
              );
            }}
          >
            Request Personnel
          </TabItem>
          <TabItem
            active={activeTab === "Status"}
            onClick={() => {
              setActiveTab("Status");
              push(`/dashboard/companies/${params.companyId}/details/status`);
            }}
          >
            Status
          </TabItem>
          <TabItem
            active={activeTab === "Notes"}
            onClick={() => {
              setActiveTab("Notes");
              push(`/dashboard/companies/${params.companyId}/details/notes`);
            }}
          >
            Notes
          </TabItem>
          <TabItem
            active={activeTab === "Postings"}
            onClick={() => {
              setActiveTab("Postings");
              push(`/dashboard/companies/${params.companyId}/details/postings`);
            }}
          >
            Postings
          </TabItem>
        </TabContent>
      </TabsContainer>

      {children}

      {showRejectModal && (
        <ModalOverlay>
          <ModalContainer>
            <ModalHeader>Confirm Rejection</ModalHeader>
            <ModalContent>
              Are you sure you want to reject this institution?
            </ModalContent>
            <ModalActions>
              <ModalButton
                variant="cancel"
                onClick={() => setShowRejectModal(false)}
              >
                Cancel
              </ModalButton>
              <ModalButton variant="reject" onClick={handleReject}>
                Reject
              </ModalButton>
            </ModalActions>
          </ModalContainer>
        </ModalOverlay>
      )}
    </Container>
  );
}

const Container = styled.div`
  min-height: 100vh;
  padding-bottom: 2rem;
  margin: 0 auto;
  background-color: #f5f7fa;
`;

const BackButtonContainer = styled.div`
  padding: 1rem 2rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #000;
  padding: 0;
`;

const TabsContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  padding: 12px 22px;
  margin-top: 1rem;
  background: #ffffff;
  max-width: 615px;
  border-radius: 25px;
  margin-left: 30px;
  margin-bottom: 1.5rem;
`;

const TabContent = styled.div`
  display: flex;
  background: #f7f7f7;
  border-radius: 20px;
  padding: 6px 14px;
`;

const TabItem = styled.div`
  padding: 0.75rem 1.25rem;
  font-size: 14px;
  cursor: pointer;
  position: relative;
  color: ${(props) => (props.active ? "#16a34a" : "#666")};
  font-weight: ${(props) => (props.active ? "600" : "400")};
  background-color: ${(props) => (props.active ? "#ffffff" : "transparent")};
  border-radius: 20px;
  transition: all 0.2s ease;

  &:hover {
    color: ${(props) => (props.active ? "#16a34a" : "#333")};
  }
`;

const ActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e1e4ea;
`;

const PageTitle = styled.h1`
  font-size: 16px;
  font-weight: 500;
  color: #000;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  ${(props) =>
    props.variant === "approve" &&
    `
    background-color: #119411;
    border: none;
    color: #fff;
  `}

  ${(props) =>
    props.variant === "reject" &&
    `
    background-color: #ff4d4f;
    color: #fff;
    border: none;
  `}
  
  ${(props) =>
    props.variant === "more" &&
    `
    background: none;
    border: none;
    color: #666;
    padding: 0.5rem;
    &:hover {
      background-color: #f5f5f5;
    }
  `}
`;

const DropdownContainer = styled.div`
  position: relative;
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background-color: white;
  border: 1px solid #e1e4ea;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 1000;
`;

const DropdownItem = styled.button`
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #e1e4ea;
  font-weight: 600;
  font-size: 18px;
`;

const ModalContent = styled.div`
  padding: 1.5rem 1rem;
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  gap: 0.5rem;
  border-top: 1px solid #e1e4ea;
`;

const ModalButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;

  ${(props) =>
    props.variant === "cancel" &&
    `
    background-color: #f5f5f5;
    border: 1px solid #e1e4ea;
    color: #333;
  `}

  ${(props) =>
    props.variant === "reject" &&
    `
    background-color: #ff4d4f;
    border: none;
    color: white;
  `}
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;
