"use client";

import React, { useState, useEffect } from "react";
import styled from "styled-components";
import { DataTable } from "@globalicons/enterprise-tools";
import { FileText, InboxIcon, MoreVertical } from "lucide-react";
import { useRouter } from "next/navigation";
import { api } from "@/api/client";

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

export default function SpecialPostingPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [specialPostings, setSpecialPostings] = useState([]);
  const [openDropdownId, setOpenDropdownId] = useState(null);

  useEffect(() => {
    const fetchSpecialPostings = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API call when available
        // const response = await api.listSpecialPostings();
        // setSpecialPostings(response.data || []);

        // Mock data for demonstration
        setTimeout(() => {
          setSpecialPostings([
            {
              id: "1",
              companyName: "Global Tech Solutions",
              companyEmail: "<EMAIL>",
              requestNumber: "SP-2023-001",
              personnelName: "John Doe",
              dateRequested: "2023-09-15",
              status: "Pending",
            },
            {
              id: "2",
              companyName: "Accra Medical Center",
              companyEmail: "<EMAIL>",
              requestNumber: "SP-2023-002",
              personnelName: "Jane Smith",
              dateRequested: "2023-09-18",
              status: "Approved",
            },
            {
              id: "3",
              companyName: "Ghana Education Services",
              companyEmail: "<EMAIL>",
              requestNumber: "SP-2023-003",
              personnelName: "Michael Johnson",
              dateRequested: "2023-09-20",
              status: "Rejected",
            },
            {
              id: "4",
              companyName: "Tema Port Authority",
              companyEmail: "<EMAIL>",
              requestNumber: "SP-2023-004",
              personnelName: "Sarah Williams",
              dateRequested: "2023-09-22",
              status: "Pending",
            },
            {
              id: "5",
              companyName: "Bank of Ghana",
              companyEmail: "<EMAIL>",
              requestNumber: "SP-2023-005",
              personnelName: "David Mensah",
              dateRequested: "2023-09-25",
              status: "Approved",
            },
          ]);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error("Error fetching special postings:", error);
        setIsLoading(false);
      }
    };

    fetchSpecialPostings();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  const handleViewDetails = (posting) => {
    // Navigate to details page or open modal
    router.push(`/dashboard/students/special-posting/${posting.id}`);
  };

  const columns = [
    {
      id: "companyName",
      accessorKey: "companyName",
      header: "Company Name",
      filterType: "select",
    },
    {
      id: "companyEmail",
      accessorKey: "companyEmail",
      header: "Company Email",
    },
    {
      id: "requestNumber",
      accessorKey: "requestNumber",
      header: "NSS Request Number",
    },
    {
      id: "personnelName",
      accessorKey: "personnelName",
      header: "Personnel Name",
    },
    {
      id: "dateRequested",
      accessorKey: "dateRequested",
      header: "Date Requested",
      filterType: "date-range",
      cell: (info) => {
        const date = new Date(info.getValue());
        return date.toLocaleDateString();
      },
    },
    {
      id: "status",
      accessorKey: "status",
      header: "Status",
      filterType: "select",
      cell: (info) => {
        const status = info.getValue();
        let color;

        switch (status) {
          case "Approved":
            color = "#16a34a"; // green
            break;
          case "Rejected":
            color = "#ef4444"; // red
            break;
          default:
            color = "#f59e0b"; // amber for pending
        }

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {status}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "absolute",
                  right: 0,
                  top: "100%",
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewDetails(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <Container>
      <TableContainer>
        <TableHeader>
          <TitleWrapper>
            <FileText size={20} color="#6b7280" />
            <TableTitle>Uploaded Entries</TableTitle>
          </TitleWrapper>
        </TableHeader>

        <TableContent>
          {isLoading ? (
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading special posting requests...</LoaderText>
            </LoaderContainer>
          ) : specialPostings.length === 0 ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={40} color="#6b7280" />
              </EmptyStateIcon>
              <EmptyStateText>No special posting requests found</EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              data={specialPostings}
              columns={columns}
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              pageSize={10}
              isDownloadable={{ formats: ["csv", "pdf"] }}
            />
          )}
        </TableContent>
      </TableContainer>
    </Container>
  );
}
