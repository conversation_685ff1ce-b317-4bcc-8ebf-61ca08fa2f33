"use client";
import React, { useEffect, useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { queries } from "@/api/client";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import { DataTable } from "@globalicons/enterprise-tools";
import { FileText, InboxIcon, MoreVertical } from "lucide-react";
import PeakDetails from "@/components/dashboard/dataTable/PeakDetails";

// Styled components (same as students page)
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const TableContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const TitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const TableHeader = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  gap: 1.75rem;
`;

const TableTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 0.75rem;
`;

const FilterButton = styled.button`
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background: none;
  border: none;
  color: ${(props) => {
    if (!props.$active) return "#6b7280";
    return props.$type === "archived" ? "#ef4444" : "#16a34a";
  }};
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: ${(props) => {
      if (!props.$active) return "transparent";
      return props.$type === "archived" ? "#ef4444" : "#16a34a";
    }};
    transform: scaleX(${(props) => (props.$active ? 1 : 0)});
    transition: transform 0.2s;
  }

  &:hover {
    color: ${(props) => (props.$type === "archived" ? "#ef4444" : "#16a34a")};
  }
`;

const TableContent = styled.div`
  padding: 1rem;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 1rem;
`;

const EmptyStateText = styled.p`
  font-size: 1rem;
  font-weight: 500;
`;

// Add these styled components after your existing styled components
const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

const InstitutionsTable = () => {
  const { push } = useRouter();
  const queryClient = useQueryClient();
  const [activeFilter, setActiveFilter] = useState("active");
  const [selectedInstitution, setSelectedInstitution] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    const token = sessionStorage.getItem("authToken");
    if (!token) {
      push("/login");
    } else {
      queryClient.invalidateQueries(["institutions"]);
    }
  }, [push, queryClient]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (openDropdownId && !event.target.closest(".dropdown")) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, [openDropdownId]);

  const handleArchive = (institution) => {
    console.log("Archiving institution:", institution);
  };

  const {
    data: institutionsData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listInstitutions,
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  console.log(institutionsData, "Inst");

  const columns = [
    {
      id: "name",
      accessorKey: "name",
      header: "Name",
      filterType: "select",
    },
    {
      id: "address",
      accessorKey: "address",
      header: "Address",
    },
    {
      id: "email",
      accessorKey: "email",
      header: "Email",
    },
    {
      id: "numberOfStudents",
      accessorKey: "numberOfStudents",
      header: "Number Of Students Submitted",
    },
    {
      id: "Status",
      accessorKey: "approvalStatus",
      header: "Status",
      filterType: "select",
      cell: (info) => {
        const status = info.getValue();
        let color;

        switch (status.toLowerCase()) {
          case "approved":
            color = "#16a34a"; // green
            break;
          case "rejected":
            color = "#ef4444"; // red
            break;
          case "archived":
            color = "#6b7280"; // gray
            break;
          default:
            color = "#f59e0b"; // amber for pending
        }

        return (
          <span
            style={{
              color: color,
              fontWeight: 500,
              padding: "0.25rem 0.5rem",
              borderRadius: "0.25rem",
              backgroundColor: `${color}15`,
            }}
          >
            {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
          </span>
        );
      },
    },
    {
      id: "createdAt",
      accessorKey: "createdAt",
      header: "Registration Date",
      filterType: "date-range",
    },
    {
      id: "actions",
      header: "Actions",
      size: 100,
      cell: (info) => (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            position: "relative",
          }}
        >
          <div className="dropdown">
            <button
              style={{
                background: "none",
                border: "none",
                cursor: "pointer",
                padding: "4px",
                borderRadius: "4px",
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Calculate position for dropdown
                const rect = e.currentTarget.getBoundingClientRect();
                setDropdownPosition({
                  top: rect.bottom + window.scrollY,
                  left: rect.left - 50, // Offset to the left by 100px
                });
                setOpenDropdownId(
                  openDropdownId === info.row.id ? null : info.row.id
                );
              }}
            >
              <MoreVertical size={16} color="#6b7280" />
            </button>
            {openDropdownId === info.row.id && (
              <div
                className="dropdown-content"
                style={{
                  position: "fixed",
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                  backgroundColor: "#fff",
                  boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
                  borderRadius: "4px",
                  padding: "0.5rem 0",
                  zIndex: 1000,
                  // minWidth: "150px",
                }}
              >
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedInstitution(info.row.original);
                    setOpenPeakDrawer(true);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                  }}
                >
                  View Details
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleArchive(info.row.original);
                    setOpenDropdownId(null);
                  }}
                  style={{
                    display: "block",
                    width: "100%",
                    padding: "0.5rem 1rem",
                    textAlign: "left",
                    border: "none",
                    background: "none",
                    cursor: "pointer",
                    color: "#ef4444",
                  }}
                >
                  Archive
                </button>
              </div>
            )}
          </div>
        </div>
      ),
    },
  ];

  const handleFilterChange = (filter) => {
    setActiveFilter(filter);
  };

  // Replace the existing loading check with this
  if (isLoading) {
    return (
      <Container>
        <TableContainer>
          <TableHeader>
            <TitleWrapper>
              <FileText size={20} color="#6b7280" />
              <TableTitle>Registered Institutions</TableTitle>
            </TitleWrapper>
            <ButtonGroup>
              <FilterButton $active={activeFilter === "active"} $type="active">
                Active
              </FilterButton>
              <FilterButton
                $active={activeFilter === "archived"}
                $type="archived"
              >
                Archived
              </FilterButton>
            </ButtonGroup>
          </TableHeader>
          <TableContent>
            <LoaderContainer>
              <LoaderSpinner />
              <LoaderText>Loading institutions...</LoaderText>
            </LoaderContainer>
          </TableContent>
        </TableContainer>
      </Container>
    );
  }

  if (error) {
    return <div>Error loading institutions: {error.message}</div>;
  }

  return (
    <Container>
      <TableContainer>
        <TableHeader>
          <TitleWrapper>
            <FileText size={20} color="#6b7280" />
            <TableTitle>Registered Institutions</TableTitle>
          </TitleWrapper>
          <ButtonGroup>
            <FilterButton
              $active={activeFilter === "active"}
              $type="active"
              onClick={() => handleFilterChange("active")}
            >
              Active
            </FilterButton>
            <FilterButton
              $active={activeFilter === "archived"}
              $type="archived"
              onClick={() => handleFilterChange("archived")}
            >
              Archived
            </FilterButton>
          </ButtonGroup>
        </TableHeader>

        <TableContent>
          {activeFilter === "archived" ? (
            <EmptyState>
              <EmptyStateIcon>
                <InboxIcon size={64} color="#9ca3af" />
              </EmptyStateIcon>
              <EmptyStateText>No archived institutions found</EmptyStateText>
            </EmptyState>
          ) : (
            <DataTable
              data={institutionsData?.data || []}
              columns={columns}
              theme="nss"
              enableColumnVisibility
              enableRowSelection
              enablePagination
              enableFilters
              isSearchable
              isDownloadable={{ formats: ["csv", "pdf"] }}
              onRowClick={(row) => {
                setOpenPeakDrawer(false);
                setSelectedInstitution(row);
                setOpenPeakDrawer(true);
              }}
            />
          )}
        </TableContent>
      </TableContainer>
      <PeakDetails
        institution={selectedInstitution}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />
    </Container>
  );
};

export default InstitutionsTable;
