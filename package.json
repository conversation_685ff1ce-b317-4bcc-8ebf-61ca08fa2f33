{"name": "nss-back-office-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@globalicons/enterprise-tools": "^3.2.0", "@headlessui/react": "^2.2.0", "@tanstack/react-query": "^5.68.0", "axios": "^1.8.3", "lucide-react": "^0.482.0", "moment": "^2.30.1", "next": "15.2.2", "nextjs-progressbar": "^0.0.16", "nextjs-toploader": "^3.8.16", "nprogress": "^0.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-select": "^5.10.1", "recharts": "^2.15.3", "styled-components": "^6.1.16"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/nprogress": "^0.2.3", "eslint": "^9", "eslint-config-next": "15.2.2"}}