import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";
import NProgress from "nprogress"; // Import NProgress

import {
  HomeIcon,
  Users2Icon,
  BuildingIcon,
  LayersIcon,
  SettingsIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ChevronLeftIcon,
  Network,
} from "lucide-react";
import {
  ChevronWrapper,
  CollapseButtonContainer,
  IconWrapper,
  Label,
  LogoContainer,
  LogoImage,
  MenuContainer,
  MenuItemButton,
  MenuItemContainer,
  OnlineIndicator,
  SidebarContainer,
  SubMenuContainer,
  UserEmail,
  UserImage,
  UserImageContainer,
  UserInfo,
  UserName,
  UserProfileContainer,
  UserRole,
} from "../styles/dashboard/sidebar.styled";

const CollapseButton = styled.button`
  width: 28px;
  height: 28px;
  padding: 4px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const MenuTooltip = styled.span`
  position: fixed;
  left: 70px;
  background-color: #119411;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -4px;
    transform: translateY(-50%) rotate(45deg);
    width: 8px;
    height: 8px;
    background-color: #119411;
  }
`;

// Component implementations
const MenuItem = ({
  icon,
  label,
  isActive,
  onClick,
  children,
  id,
  activeMenuId,
  setActiveMenuId,
  isCollapsed,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0 });
  const hasChildren = children && children.length > 0;
  const menuItemRef = useRef(null);

  const handleClick = () => {
    if (hasChildren) {
      setIsOpen(!isOpen);
    } else {
      setActiveMenuId(id);
    }
    if (onClick) onClick();
  };

  const handleMouseEnter = () => {
    if (menuItemRef.current && isCollapsed) {
      const rect = menuItemRef.current.getBoundingClientRect();
      setTooltipPosition({ top: rect.top + rect.height / 2 });
      setShowTooltip(true);
    }
  };

  return (
    <MenuItemContainer>
      <MenuItemButton
        ref={menuItemRef}
        $isActive={isActive || activeMenuId === id}
        onClick={handleClick}
        $isCollapsed={isCollapsed}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={() => setShowTooltip(false)}
      >
        <IconWrapper
          $isActive={isActive || activeMenuId === id}
          $isCollapsed={isCollapsed}
        >
          {icon}
        </IconWrapper>
        {!isCollapsed && <Label>{label}</Label>}
        {hasChildren && !isCollapsed && (
          <ChevronWrapper $isActive={isActive || activeMenuId === id}>
            {isOpen ? (
              <ChevronDownIcon size={20} />
            ) : (
              <ChevronRightIcon size={20} />
            )}
          </ChevronWrapper>
        )}
        {isCollapsed && showTooltip && (
          <MenuTooltip style={{ top: tooltipPosition.top }}>
            {label}
          </MenuTooltip>
        )}
      </MenuItemButton>

      {hasChildren && isOpen && !isCollapsed && (
        <SubMenuContainer>{children}</SubMenuContainer>
      )}
    </MenuItemContainer>
  );
};

const Sidebar = ({ userImage, onMenuItemClick }) => {
  const router = useRouter();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [user, setUser] = useState({
    name: "",
    email: "",
    profileImage: "/profile-pic-placeholder.svg",
    role: "",
  });

  // Add useEffect to get email from sessionStorage
  useEffect(() => {
    // Get user data from sessionStorage
    const user = sessionStorage.getItem("user")
      ? JSON.parse(sessionStorage.getItem("user"))
      : null;
    const token = sessionStorage.getItem("authToken");

    if (!token) {
      router.push("/login");
      return;
    }

    if (user.email) {
      const parsedEmail = user.email;
      setUser({
        name: user.fullName,
        email: parsedEmail || "",
        profileImage: "/profile-pic-placeholder.svg",
        role: user.role,
      });
    }
  }, [router]);

  const [activeMenuId, setActiveMenuId] = useState(() => {
    // Get initial active state based on current path
    if (typeof window !== "undefined") {
      // Ensure window is defined (client-side)
      const path = window.location.pathname;
      if (path.includes("/students")) return "personnel";
      if (path.includes("/institutions")) return "institutions";
      if (path.includes("/batches")) return "enrollments";
      if (path.includes("/settings")) return "settings";
      if (path.includes("/companies")) return "companies";
      if (path.includes("/roles-users")) return "roles-users"; // Add this line
      if (path.includes("/districts")) return "districts"; // Add this line for districts
    }
    return "overview"; // Default active menu item
  });

  const handleNavigation = (path, menuId) => {
    NProgress.start(); // Start the progress bar
    router.push(`/dashboard${path}`);
    setActiveMenuId(menuId);
    if (onMenuItemClick) {
      onMenuItemClick(path);
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <SidebarContainer $isCollapsed={isCollapsed}>
      <LogoContainer $isCollapsed={isCollapsed}>
        <LogoImage
          $isCollapsed={isCollapsed}
          src="/logos/NSALogo.png"
          alt="Logo"
        />
      </LogoContainer>

      <CollapseButtonContainer $isCollapsed={isCollapsed}>
        <CollapseButton onClick={toggleCollapse}>
          {isCollapsed ? (
            <ChevronRightIcon size={16} />
          ) : (
            <ChevronLeftIcon size={16} />
          )}
        </CollapseButton>
      </CollapseButtonContainer>

      <MenuContainer $isCollapsed={isCollapsed}>
        <MenuItem
          icon={<HomeIcon size={20} />}
          label="Overview"
          id="overview"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/", "overview")}
          isCollapsed={isCollapsed}
        />

        <MenuItem
          icon={<LayersIcon size={20} />}
          label="Enrollments"
          id="enrollments"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/batches", "enrollments")}
          isCollapsed={isCollapsed}
        />

        <MenuItem
          icon={<Users2Icon size={20} />}
          label="Personnel"
          id="personnel"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/students", "personnel")}
          isCollapsed={isCollapsed}
        />

        <MenuItem
          icon={<BuildingIcon size={20} />}
          label="Institutions"
          id="institutions"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/institutions", "institutions")}
          isCollapsed={isCollapsed}
        />

        <MenuItem
          icon={<Network size={20} />}
          label="User Agencies "
          id="companies"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/companies", "companies")}
          isCollapsed={isCollapsed}
        />

        {/* New MenuItem for Roles and Users */}
        <MenuItem
          icon={<Users2Icon size={20} />} // Placeholder icon, change as needed
          label="Roles and Users"
          id="roles-users" // Unique ID for the new menu item
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/roles-users", "roles-users")} // Placeholder path, change as needed
          isCollapsed={isCollapsed}
        />

        {/* New MenuItem for District Management */}
        <MenuItem
          icon={<BuildingIcon size={20} />} // Using BuildingIcon for district centers
          label="Manage Centers"
          id="districts" // Unique ID for the district menu item
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/districts", "districts")}
          isCollapsed={isCollapsed}
        />

        <MenuItem
          icon={<SettingsIcon size={20} />}
          label="Account Settings"
          id="settings"
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          onClick={() => handleNavigation("/settings", "settings")}
          isCollapsed={isCollapsed}
        />
      </MenuContainer>

      <UserProfileContainer $isCollapsed={isCollapsed}>
        <UserImageContainer>
          <UserImage src={userImage || "/images/avatar.png"} alt="User" />
          <OnlineIndicator />
        </UserImageContainer>
        {!isCollapsed && (
          <UserInfo>
            <UserName>{user.name}</UserName>
            <UserRole>
              {" "}
              {user.role
                ? user.role
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                    .join(" ")
                : ""}
            </UserRole>
            <UserEmail
              style={{
                maxWidth: "150px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {/* {userEmail} */}
            </UserEmail>
          </UserInfo>
        )}
      </UserProfileContainer>
    </SidebarContainer>
  );
};

export default Sidebar;
