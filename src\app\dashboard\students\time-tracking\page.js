"use client";

import React, { useState } from "react";
import styled from "styled-components";
import { ChevronLeft, ChevronRight, Printer } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { queries } from "@/api/client";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LabelList,
  Cell,
} from "recharts";

// Styled components for the time tracking dashboard
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
`;

const PageTitle = styled.h1`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1.5rem;
`;

const DashboardCard = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  padding: 1.5rem;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const TotalTimeSection = styled.div`
  display: flex;
  flex-direction: column;
`;

const TotalTimeLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
`;

const TotalTimeValue = styled.div`
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
`;

const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 0.375rem;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const NavigationButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const NavButton = styled.button`
  background: none;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const ChartContainer = styled.div`
  height: 300px;
  width: 100%;
  margin-top: 1rem;

  .recharts-rectangle.recharts-bar-rectangle {
    transition: fill 0.3s ease;
    cursor: pointer;
  }

  .recharts-rectangle.recharts-bar-rectangle:hover {
    fill: #d0e8d0 !important;
  }
`;

// New styled components for the action container
const ActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: white;
  padding: 1rem 1.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #000;
  font-size: 0.875rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;

  /* &:hover {
    background-color: #f3f4f6;
  } */
`;

// Custom tooltip component for the chart
const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: "white",
          padding: "10px",
          border: "1px solid #e5e7eb",
          borderRadius: "4px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <p style={{ margin: 0 }}>{`Time: ${payload[0].payload.time}`}</p>
      </div>
    );
  }
  return null;
};

export default function TimeTrackingDashboard() {
  const params = useParams();
  const router = useRouter();
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const studentId = params.studentId;

  // Mock data for the time tracking
  const timeData = [
    { day: "Mon, Mar 3", hours: 3, time: "03:00:00", isActive: false },
    { day: "Tue, Mar 4", hours: 1, time: "01:00:00", isActive: false },
    { day: "Wed, Mar 5", hours: 6, time: "06:00:00", isActive: true },
    { day: "Thu, Mar 6", hours: 5, time: "05:00:00", isActive: false },
    { day: "Fri, Mar 7", hours: 1, time: "01:00:00", isActive: false },
    { day: "Sat, Mar 8", hours: 4, time: "04:00:00", isActive: false },
    { day: "Sun, Mar 9", hours: 5, time: "05:00:00", isActive: false },
  ];

  // Calculate total time
  const totalHours = timeData.reduce((sum, day) => sum + day.hours, 0);
  const formatTotalTime = () => {
    const hours = Math.floor(totalHours);
    const minutes = Math.floor((totalHours - hours) * 60);
    const seconds = Math.floor(((totalHours - hours) * 60 - minutes) * 60);
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek);
    prevWeek.setDate(prevWeek.getDate() - 7);
    setCurrentWeek(prevWeek);
  };

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek);
    nextWeek.setDate(nextWeek.getDate() + 7);
    setCurrentWeek(nextWeek);
  };

  return (
    <>
      <ActionContainer>
        <BackButton onClick={() => router.back()}>
          <ChevronLeft size={16} />
          Back
        </BackButton>
        <ButtonGroup>
          <ActionButton
            onClick={() => {
              window.print();
            }}
          >
            <Printer size={16} />
            Print Page
          </ActionButton>
        </ButtonGroup>
      </ActionContainer>
      <Container>
        <DashboardCard>
          <CardHeader>
            <TotalTimeSection>
              <TotalTimeLabel>Total Time Clocked:</TotalTimeLabel>
              <TotalTimeValue>{formatTotalTime()}</TotalTimeValue>
            </TotalTimeSection>

            <ActionsContainer>
              <ActionButton>Actions ▼</ActionButton>

              <NavigationButtons>
                <NavButton onClick={goToPreviousWeek}>
                  <ChevronLeft size={16} />
                </NavButton>
                <ActionButton>Last Week</ActionButton>
                <NavButton onClick={goToNextWeek}>
                  <ChevronRight size={16} />
                </NavButton>
              </NavigationButtons>
            </ActionsContainer>
          </CardHeader>

          <ChartContainer>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={timeData}
                margin={{
                  top: 30,
                  right: 30,
                  left: 20,
                  bottom: 20,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="day"
                  tick={{ fontSize: 12, fill: "#9ca3af" }}
                  axisLine={{ stroke: "#e5e7eb" }}
                  tickLine={false}
                />
                <YAxis
                  tick={{ fontSize: 12, fill: "#9ca3af" }}
                  axisLine={{ stroke: "#e5e7eb" }}
                  tickLine={false}
                  tickFormatter={(value) => `${value}h`}
                />
                <Tooltip
                  content={<CustomTooltip />}
                  cursor={{ fill: "#1194111A" }}
                />
                <Bar dataKey="hours" radius={[4, 4, 0, 0]} maxBarSize={100}>
                  {timeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill="#119411" />
                  ))}
                  <LabelList
                    dataKey="time"
                    position="top"
                    style={{ fontSize: "12px", fill: "#6b7280" }}
                  />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </DashboardCard>
      </Container>
    </>
  );
}
