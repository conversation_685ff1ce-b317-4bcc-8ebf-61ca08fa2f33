"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import styled from "styled-components";

// Container Components
const PageContainer = styled.div`
  padding: 24px;
  /* max-width: 1200px; */
  margin: 0 auto;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #111827;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled.button`
  background-color: #f9fafb;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f3f4f6;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  overflow-x: auto;
  padding: 4px 0;
`;

const FilterButton = styled.button`
  background-color: ${(props) => (props.active ? "#eff6ff" : "transparent")};
  color: ${(props) => (props.active ? "#2563eb" : "#6b7280")};
  border: ${(props) =>
    props.active ? "1px solid #bfdbfe" : "1px solid #e5e7eb"};
  border-radius: 20px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;

  &:hover {
    background-color: ${(props) => (props.active ? "#eff6ff" : "#f9fafb")};
  }
`;

const NotificationsContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
`;

const NotificationItem = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: ${(props) => (props.read ? "white" : "#f9fafb")};
  display: flex;
  gap: 16px;
  position: relative;
`;

const TypeIcon = styled.div`
  background-color: ${(props) => {
    switch (props.type) {
      case "success":
        return "#ecfdf5";
      case "warning":
        return "#fffbeb";
      case "error":
        return "#fef2f2";
      default:
        return "#eff6ff";
    }
  }};
  color: ${(props) => {
    switch (props.type) {
      case "success":
        return "#059669";
      case "warning":
        return "#d97706";
      case "error":
        return "#dc2626";
      default:
        return "#3b82f6";
    }
  }};
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const ContentContainer = styled.div`
  flex: 1;
`;

const ContentHeader = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 8px;
`;

const UnreadIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ef4444;
  margin-top: 6px;
  flex-shrink: 0;
`;

const NotificationTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.4;
`;

const NotificationSubtitle = styled.div`
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
`;

const NotificationFooter = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const TimeStamp = styled.div`
  font-size: 13px;
  color: #9ca3af;
`;

const ActionContainer = styled.div`
  display: flex;
  gap: 12px;
`;

const TextButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    color: #4b5563;
  }
`;

const ActionLink = styled.a`
  font-size: 14px;
  color: #2563eb;
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;

  &:hover {
    text-decoration: underline;
  }
`;

const DeleteButton = styled.button`
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;

  &:hover {
    background-color: #f3f4f6;
    color: #4b5563;
  }
`;

const EmptyState = styled.div`
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
`;

const EmptyStateIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
`;

const EmptyStateTitle = styled.h3`
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 8px;
`;

const EmptyStateText = styled.p`
  font-size: 14px;
  max-width: 400px;
  margin: 0 auto;
`;

const PaginationInfo = styled.div`
  display: flex;
  justify-content: center;
  padding: 24px 0;
  color: #6b7280;
  font-size: 14px;
`;

const NotificationsPage = () => {
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState("all");
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title:
        "Your request for personnel under the 2024/2025 batch has been submitted.",
      subtitle: "You'll be notified when postings are released.",
      time: "8 min ago",
      read: false,
      type: "info",
      action: null,
    },
    {
      id: 2,
      title: "Postings for your company are now available.",
      subtitle: "Kindly review dashboard to view assigned personnel.",
      time: "10 min ago",
      read: false,
      type: "success",
      action: {
        text: "View Postings",
        url: "/dashboard/postings",
      },
    },
    {
      id: 3,
      title: "You haven't completed your application for the current batch.",
      subtitle: "Deadline: March 30, 2025.",
      time: "10 min ago",
      read: false,
      type: "warning",
      action: {
        text: "Complete Application",
        url: "/dashboard/application",
      },
    },
    {
      id: 4,
      title: "New personnel have been assigned to your organization.",
      subtitle: "3 new personnel have been added to your roster.",
      time: "1 day ago",
      read: true,
      type: "info",
      action: {
        text: "View Personnel",
        url: "/dashboard/personnel",
      },
    },
    {
      id: 5,
      title: "System maintenance scheduled for tomorrow.",
      subtitle: "The system will be unavailable from 2AM to 4AM GMT.",
      time: "2 days ago",
      read: true,
      type: "warning",
      action: null,
    },
    {
      id: 6,
      title: "Your quarterly report is due next week.",
      subtitle: "Please submit your quarterly report by June 30th.",
      time: "3 days ago",
      read: true,
      type: "info",
      action: {
        text: "Submit Report",
        url: "/dashboard/reports/submit",
      },
    },
  ]);

  const markAsRead = (id) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({ ...notification, read: true }))
    );
  };

  const deleteNotification = (id) => {
    setNotifications(
      notifications.filter((notification) => notification.id !== id)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const filteredNotifications = notifications.filter((notification) => {
    if (activeFilter === "all") return true;
    if (activeFilter === "unread") return !notification.read;
    if (activeFilter === "read") return notification.read;
    return notification.type === activeFilter;
  });

  const getTypeIcon = (type) => {
    switch (type) {
      case "success":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "warning":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12 9V11M12 15H12.01M5.07183 19H18.9282C20.4678 19 21.4301 17.3333 20.6603 16L13.7321 4C12.9623 2.66667 11.0378 2.66667 10.268 4L3.33978 16C2.56998 17.3333 3.53223 19 5.07183 19Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        );
      case "error":
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10 14L12 12M12 12L14 10M12 12L10 10M12 12L14 14M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        );
      default:
        return (
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M13 16H12V12H11M12 8H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        );
    }
  };

  return (
    <PageContainer>
      <HeaderContainer>
        <ButtonGroup>
          <ActionButton onClick={markAllAsRead}>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Mark all as read
          </ActionButton>
          <ActionButton onClick={clearAllNotifications}>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M19 7L18.1327 19.1425C18.0579 20.1891 17.187 21 16.1378 21H7.86224C6.81296 21 5.94208 20.1891 5.86732 19.1425L5 7M10 11V17M14 11V17M15 7V4C15 3.44772 14.5523 3 14 3H10C9.44772 3 9 3.44772 9 4V7M4 7H20"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Clear all
          </ActionButton>
        </ButtonGroup>
      </HeaderContainer>

      <FilterContainer>
        {["all", "unread", "read", "info", "success", "warning", "error"].map(
          (filter) => (
            <FilterButton
              key={filter}
              active={activeFilter === filter}
              onClick={() => setActiveFilter(filter)}
            >
              {filter.charAt(0).toUpperCase() + filter.slice(1)}
            </FilterButton>
          )
        )}
      </FilterContainer>

      <NotificationsContainer>
        {filteredNotifications.length > 0 ? (
          filteredNotifications.map((notification) => (
            <NotificationItem key={notification.id} read={notification.read}>
              <TypeIcon type={notification.type}>
                {getTypeIcon(notification.type)}
              </TypeIcon>

              <ContentContainer>
                <ContentHeader>
                  {!notification.read && <UnreadIndicator />}
                  <div style={{ flex: 1 }}>
                    <NotificationTitle>{notification.title}</NotificationTitle>
                    <NotificationSubtitle>
                      {notification.subtitle}
                    </NotificationSubtitle>

                    <NotificationFooter>
                      <TimeStamp>{notification.time}</TimeStamp>

                      <ActionContainer>
                        {!notification.read && (
                          <TextButton
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </TextButton>
                        )}

                        {notification.action && (
                          <ActionLink href={notification.action.url}>
                            {notification.action.text}
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M9 18L15 12L9 6"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </ActionLink>
                        )}
                      </ActionContainer>
                    </NotificationFooter>
                  </div>
                </ContentHeader>
              </ContentContainer>

              <DeleteButton onClick={() => deleteNotification(notification.id)}>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 18L18 6M6 6L18 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </DeleteButton>
            </NotificationItem>
          ))
        ) : (
          <EmptyState>
            <EmptyStateIcon>
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.36 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.63 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16Z"
                  fill="currentColor"
                />
              </svg>
            </EmptyStateIcon>
            <EmptyStateTitle>No notifications</EmptyStateTitle>
            <EmptyStateText>
              You don&apos;t have any notifications at the moment. We&apos;ll
              notify you when something important happens.
            </EmptyStateText>
          </EmptyState>
        )}
      </NotificationsContainer>

      {filteredNotifications.length > 0 && (
        <PaginationInfo>
          Showing {filteredNotifications.length} of {notifications.length}{" "}
          notifications
        </PaginationInfo>
      )}
    </PageContainer>
  );
};

export default NotificationsPage;
