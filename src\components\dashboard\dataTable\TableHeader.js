// src/components/DataTable/TableHeader.js
import React from "react";
import styled from "styled-components";

// Styled Components
const HeaderRow = styled.tr`
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
`;

const HeaderCell = styled.th`
  padding: 0.75rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: ${(props) => (props.$sortable ? "pointer" : "default")};

  &:hover {
    background-color: ${(props) =>
      props.$sortable ? "#e5e7eb" : "transparent"};
  }
`;

const CheckboxCell = styled.th`
  padding: 0.75rem;
  width: 2.5rem;
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  white-space: nowrap;
`;

const Checkbox = styled.input`
  width: 1rem;
  height: 1rem;
  color: #2563eb;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
`;

/**
 * TableHeader - Renders the header row of the data table with sorting capabilities
 * @param {Object} props - Component props
 * @param {Array} props.columns - Array of column configuration objects
 * @param {Object} props.sortConfig - Current sort configuration
 * @param {Function} props.onSort - Function to call when column header is clicked for sorting
 * @param {Function} props.onSelectAll - Function to call when select all checkbox is toggled
 * @param {Boolean} props.allSelected - Whether all rows on current page are selected
 */
const TableHeader = ({
  columns,
  sortConfig,
  onSort,
  onSelectAll,
  allSelected,
}) => {
  return (
    <thead>
      <HeaderRow>
        {/* Select all checkbox column */}
        <CheckboxCell>
          <Checkbox
            type="checkbox"
            checked={allSelected}
            onChange={onSelectAll}
          />
        </CheckboxCell>

        {/* Column headers */}
        {columns.map((column) => (
          <HeaderCell
            key={column.key}
            onClick={() =>
              column.sortable !== false ? onSort(column.key) : null
            }
            $sortable={column.sortable !== false}
          >
            <HeaderContent>
              {column.header || column.key}

              {/* Sort indicators */}
              {sortConfig.key === column.key &&
                (sortConfig.direction === "ascending" ? (
                  <svg
                    style={{
                      marginLeft: "0.25rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    style={{
                      marginLeft: "0.25rem",
                      width: "1rem",
                      height: "1rem",
                    }}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                ))}
            </HeaderContent>
          </HeaderCell>
        ))}

        {/* Actions column */}
        <CheckboxCell></CheckboxCell>
      </HeaderRow>
    </thead>
  );
};

export default TableHeader;
