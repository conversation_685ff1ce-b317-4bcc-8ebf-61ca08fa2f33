import styled from "styled-components";

export const SidebarContainer = styled.div`
  width: ${(props) => (props.$isCollapsed ? "90px" : "260px")};
  height: 100%;
  min-width: ${(props) => (props.$isCollapsed ? "80px" : "16rem")};
  background-color: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
`;

export const LogoContainer = styled.div`
  padding: ${(props) => (props.$isCollapsed ? "1.5rem 1rem" : "1.5rem ")};
  display: flex;
  align-items: center;
  justify-content: ${(props) => (props.$isCollapsed ? "center" : "flex-start")};
  position: relative;
  height: 13px; // Increased from 13px to provide more space
`;

export const MenuContainer = styled.div`
  flex-grow: 1;
  padding: ${(props) => (props.$isCollapsed ? "1.5rem 0.5rem" : "1.5rem 1rem")};
  overflow-y: auto;
  overflow-x: hidden; // Hide horizontal scrollbar
`;

export const LogoImage = styled.img`
  width: ${(props) => (props.$isCollapsed ? "100px" : "100px")};
  max-width: 100%;
`;

export const CollapseButtonContainer = styled.div`
  display: flex;
  justify-content: ${(props) => (props.$isCollapsed ? "center" : "flex-end")};
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  background: #f7f1f4;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
`;

// export const MenuContainer = styled.div`
//   flex-grow: 1;
//   padding: ${(props) => (props.$isCollapsed ? "1.5rem 0.5rem" : "1.5rem 1rem")};
//   overflow-y: auto;
// `;

export const MenuItemContainer = styled.div`
  margin-bottom: 0.5rem;
`;

export const MenuItemButton = styled.div`
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  background-color: ${(props) => (props.$isActive ? "#16a34a" : "transparent")};
  color: ${(props) => (props.$isActive ? "white" : "#4b5563")};
  justify-content: ${(props) => (props.$isCollapsed ? "center" : "flex-start")};

  &:hover {
    background-color: ${(props) => (props.$isActive ? "#16a34a" : "#f3f4f6")};
  }
`;

export const IconWrapper = styled.span`
  margin-right: ${(props) => (props.$isCollapsed ? "0" : "0.75rem")};
  color: ${(props) => (props.$isActive ? "white" : "#9ca3af")};
`;

export const Label = styled.span`
  flex-grow: 1;
`;

export const ChevronWrapper = styled.span`
  color: ${(props) => (props.$isActive ? "white" : "#9ca3af")};
`;

export const SubMenuContainer = styled.div`
  margin-left: 2.5rem;
  margin-top: 0.25rem;
`;

export const SubMenuItemButton = styled.div`
  display: flex;
  align-items: center;

  padding: 0.75rem 0.75rem;
  cursor: pointer;
  color: #4b5563;
  background-color: ${(props) => (props.$isActive ? "#10b981" : "transparent")};
  color: ${(props) => (props.$isActive ? "white" : "inherit")};
  border: none;
  border-radius: 0.5rem;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${(props) => (props.$isActive ? "#10b981" : "#f3f4f6")};
  }
`;

export const CircleMarker = styled.span`
  margin-right: 0.75rem;
  color: #d1d5db;
`;

export const UserProfileContainer = styled.div`
  margin-top: auto;
  padding: 1rem;
  display: flex;
  align-items: center;
  border-top: 1px solid #e5e7eb;
  justify-content: ${(props) => (props.$isCollapsed ? "center" : "flex-start")};
`;

export const UserImageContainer = styled.div`
  position: relative;
  margin-right: 0.75rem;
`;

export const UserImage = styled.img`
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  object-fit: cover;
`;

export const OnlineIndicator = styled.div`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.75rem;
  height: 0.75rem;
  background-color: #10b981;
  border-radius: 9999px;
  border: 2px solid white;
`;

export const UserInfo = styled.div`
  flex-grow: 1;
`;

export const UserName = styled.div`
  font-weight: 500;
  margin-bottom: 0.25rem;
`;

export const UserEmail = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
`;

export const UserRole = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
`;
