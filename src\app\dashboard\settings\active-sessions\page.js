"use client";
import {
  CloseButton,
  Container,
  Description,
  DeviceInfo,
  DeviceName,
  EmptyMessage,
  ErrorMessage,
  IconWrapper,
  Role,
  SessionCard,
  SessionInfo,
  SessionsList,
  SignOutButton,
} from "@/components/styles/dashboard/active.styled";
import { Monitor, X } from "lucide-react";

import { React, useState, useEffect } from "react";

const ActiveSessions = () => {
  // Initial sessions data
  const [sessions, setSessions] = useState([
    {
      id: 1,
      device: "Macbook Pro",
      time: "15 mins ago",
      role: "Administrator",
      lastActive: new Date(),
    },
    {
      id: 2,
      device: "Macbook Pro",
      time: "15 mins ago",
      role: "Administrator",
      lastActive: new Date(),
    },
    {
      id: 3,
      device: "Macbook Pro",
      time: "15 mins ago",
      role: "Administrator",
      lastActive: new Date(),
    },
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Function to update time display
  const getTimeAgo = (date) => {
    const minutes = Math.floor((new Date() - date) / 60000);
    if (minutes < 1) return "just now";
    if (minutes === 1) return "1 min ago";
    if (minutes < 60) return `${minutes} mins ago`;
    const hours = Math.floor(minutes / 60);
    if (hours === 1) return "1 hour ago";
    return `${hours} hours ago`;
  };

  // Function to remove a single session
  const handleRemoveSession = async (sessionId) => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Remove session from state
      setSessions((prevSessions) =>
        prevSessions.filter((session) => session.id !== sessionId)
      );
    } catch (err) {
      setError("Failed to remove session. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to sign out of all devices
  const handleSignOutAll = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Clear all sessions
      setSessions([]);
    } catch (err) {
      setError("Failed to sign out of all devices. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Update times every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setSessions((prevSessions) =>
        prevSessions.map((session) => ({
          ...session,
          time: getTimeAgo(session.lastActive),
        }))
      );
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Container>
      <Description>Monitor and manage all your active sessions.</Description>

      {error && <ErrorMessage>{error}</ErrorMessage>}

      <SessionsList>
        {sessions.map((session) => (
          <SessionCard key={session.id}>
            <SessionInfo>
              <IconWrapper>
                <Monitor />
              </IconWrapper>

              <DeviceInfo>
                <DeviceName>
                  <span>{session.device}</span>
                  <span>({session.time})</span>
                </DeviceName>
                <Role>{session.role}</Role>
              </DeviceInfo>
            </SessionInfo>

            <X
              onClick={() => handleRemoveSession(session.id)}
              disabled={isLoading}
              aria-label="Close session"
              size={18}
              color="#9ca3af"
            />
          </SessionCard>
        ))}
      </SessionsList>

      {sessions.length > 0 ? (
        <SignOutButton onClick={handleSignOutAll} disabled={isLoading}>
          {isLoading ? "Signing out..." : "Sign out of all devices"}
        </SignOutButton>
      ) : (
        <EmptyMessage>No active sessions</EmptyMessage>
      )}
    </Container>
  );
};

export default ActiveSessions;
