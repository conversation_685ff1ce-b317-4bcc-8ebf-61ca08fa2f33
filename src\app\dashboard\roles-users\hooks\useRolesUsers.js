import { useState, useEffect, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { queries } from "@/api/client";

export const useRolesUsers = () => {
  const { push } = useRouter();
  const [currentUserRole, setCurrentUserRole] = useState(null);
  const [activeFilter, setActiveFilter] = useState("national");
  const [selectedAdministrator, setSelectedAdministrator] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);

  // Get current user role
  useEffect(() => {
    const user = JSON.parse(sessionStorage.getItem("user") || "{}");
    setCurrentUserRole(user.role || null);

    // Set default filter based on role
    if (user.role === "regional_administrator") {
      setActiveFilter("regional");
    }
  }, []);

  // Fetch administrators data
  const {
    data: administratorsData,
    isLoading,
    error,
  } = useQuery({
    ...queries.listAdministrators,
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Define columns based on the active filter
  const columns = useMemo(() => {
    const baseColumns = [
      {
        id: "role",
        accessorKey: "role",
        header: "Role",
        filterType: "select",
        cell: (info) => {
          const role = info.getValue();
          // Format role for display (capitalize, replace underscores with spaces)
          return role
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
        },
      },
      {
        id: "fullName",
        accessorKey: "fullName",
        header: "Name",
      },
      {
        id: "email",
        accessorKey: "email",
        header: "Email",
      },
      {
        id: "phoneNumber",
        accessorKey: "phoneNumber",
        header: "Phone Number",
      },
      {
        id: "status",
        accessorKey: "archivedDate",
        header: "Status",
        filterType: "select",
        cell: (info) => {
          const archivedDate = info.getValue();
          let displayStatus;
          let color;

          if (archivedDate) {
            displayStatus = "Disabled";
            color = "#ef4444"; // red
          } else {
            displayStatus = "Active";
            color = "#16a34a"; // green
          }

          return (
            <span
              style={{
                color: color,
                fontWeight: 500,
                padding: "0.25rem 0.5rem",
                borderRadius: "0.25rem",
                backgroundColor: `${color}15`,
              }}
            >
              {displayStatus}
            </span>
          );
        },
      },
    ];

    // Only add the region column for regional filter
    if (
      activeFilter === "regional" ||
      activeFilter === "regionalDirector" ||
      activeFilter === "nssOfficers"
    ) {
      // Insert region column before status
      baseColumns.splice(4, 0, {
        id: "region",
        accessorKey: "region",
        header: "Region",
        filterType: "select",
        cell: (info) => {
          const region = info.getValue();
          return region || "N/A";
        },
      });
    }

    return baseColumns;
  }, [activeFilter]);

  // Filter data based on active filter and user role
  const filteredData = useMemo(() => {
    // If we don't have data yet, return an empty array
    if (!administratorsData?.data) return [];

    // For regional administrators, only show relevant data based on filter
    if (currentUserRole === "regional_administrator") {
      switch (activeFilter) {
        case "regionalDirector":
          return administratorsData.data.filter(
            (admin) => admin.role === "regional_director"
          );
        case "nssOfficers":
          return administratorsData.data.filter(
            (admin) => admin.role === "nss_officer"
          );
        default:
          return administratorsData.data.filter(
            (admin) => admin.role === "regional_director"
          );
      }
    } else {
      // Original logic for non-regional administrators
      if (activeFilter === "national") {
        return administratorsData.data.filter(
          (admin) =>
            admin.role !== "regional_administrator" &&
            admin.role !== "regional_director" &&
            admin.role !== "nss_officer"
        );
      }
      if (activeFilter === "regional") {
        return administratorsData.data.filter(
          (admin) =>
            admin.role === "regional_administrator" ||
            admin.role === "regional_director" ||
            admin.role === "nss_officer"
        );
      }
      return administratorsData.data;
    }
  }, [activeFilter, administratorsData?.data, currentUserRole]);

  return {
    currentUserRole,
    activeFilter,
    setActiveFilter,
    administratorsData,
    isLoading,
    error,
    columns,
    filteredData,
    selectedAdministrator,
    setSelectedAdministrator,
    openPeakDrawer,
    setOpenPeakDrawer,
  };
};
