"use client";

import AuthForm from "@/components/form/form";

const CheckEmail = () => {
  const buttons = [
    {
      text: "Open Email App",
      onClick: () => window.open("https://mail.google.com"),
    },
    {
      text: "Resend Link",
      onClick: () => alert("Resend link clicked!"),
    },
  ];

  const links = {
    toggleText: "Don't have an account?",
    toggleLabel: "Register",
    toggleLink: "/register",
    home: "/",
  };

  return (
    <AuthForm
      title="Check Your Email"
      subtitle="We've sent a password reset link to your email. Please check your inbox."
      links={links}
      buttons={buttons} // Use the buttons with custom styling
      bottomLinkText="Try another login" // Change text
      bottomLinkHref="/login"
    />
  );
};

export default CheckEmail;
