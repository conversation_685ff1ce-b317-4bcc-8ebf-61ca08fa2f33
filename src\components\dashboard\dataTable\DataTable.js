// src/components/DataTable/DataTable.js
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import TableHeader from './TableHeader';
import TableRow from './TableRow';
import SearchBar from './SearchBar';
import Pagination from './Pagination';
import BatchActions from './BatchActions';
import PeakDetails from './PeakDetails';

// Styled Components
const Container = styled.div`
  background-color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
`;

const Title = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;

  @media (min-width: 768px) {
    margin-bottom: 0;
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const EntryInfo = styled.div`
  background-color: #f3f4f6;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 1rem;
  border-radius: 0.25rem;
`;

const TableContainer = styled.div`
  overflow-x: auto;
`;

const StyledTable = styled.table`
  min-width: 100%;
  background-color: white;
  border-collapse: collapse;
`;

const PaginationContainer = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

/**
 * DataTable - Main component for displaying tabular data with filtering, sorting, and pagination
 * @param {Object} props - Component props
 * @param {Array} props.data - Array of data objects to display in table
 * @param {Array} props.columns - Array of column configuration objects
 * @param {Function} props.onBatchActivate - Function to call when batch action is activated
 * @param {Object} props.actions - Additional action buttons configuration
 * @param {String} props.title - Title of the data table
 */
const DataTable = ({
  data = [],
  columns = [],
  onBatchActivate = () => {},
  actions = {},
  title = 'Data Table',
  showBatchActions = true, // Add default value
}) => {
  const [filteredData, setFilteredData] = useState(data);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Calculate total pages based on filtered data
  const totalPages = Math.ceil(filteredData.length / rowsPerPage);

  // Update filtered data when original data changes
  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  // Filter data based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredData(data);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = data.filter((item) => {
        return columns.some((column) => {
          const value = item[column.key];
          return value && String(value).toLowerCase().includes(query);
        });
      });
      setFilteredData(filtered);
    }
    setCurrentPage(1); // Reset to first page on search
  }, [searchQuery, data, columns]);

  // Handle sorting of data
  const handleSort = (key) => {
    let direction = 'ascending';

    if (sortConfig.key === key) {
      direction =
        sortConfig.direction === 'ascending' ? 'descending' : 'ascending';
    }

    setSortConfig({ key, direction });

    const sortedData = [...filteredData].sort((a, b) => {
      const valueA = a[key];
      const valueB = b[key];

      if (valueA === valueB) return 0;

      if (direction === 'ascending') {
        return valueA < valueB ? -1 : 1;
      } else {
        return valueA > valueB ? -1 : 1;
      }
    });

    setFilteredData(sortedData);
  };

  // Get current page data
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  };

  // Toggle select all rows on current page
  const toggleSelectAll = () => {
    const currentPageData = getCurrentPageData();

    if (currentPageData.every((row) => selectedRows.has(row.id))) {
      // If all rows are selected, unselect them
      const newSelected = new Set(selectedRows);
      currentPageData.forEach((row) => newSelected.delete(row.id));
      setSelectedRows(newSelected);
    } else {
      // Otherwise select all rows
      const newSelected = new Set(selectedRows);
      currentPageData.forEach((row) => newSelected.add(row.id));
      setSelectedRows(newSelected);
    }
  };

  // Toggle select individual row
  const toggleSelectRow = (id) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedRows(newSelected);
  };

  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openPeakDrawer, setOpenPeakDrawer] = useState(false);

  return (
    <>
      <Container>
        {/* Table header and controls */}
        <HeaderContainer>
          <Title>{title}</Title>
          <ControlsContainer>
            <SearchBar
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search..."
            />
            {showBatchActions &&
              selectedRows.size > 0 && ( // Add conditional rendering
                <BatchActions
                  selectedCount={selectedRows.size}
                  onActivate={() => onBatchActivate(Array.from(selectedRows))}
                />
              )}
          </ControlsContainer>
        </HeaderContainer>

        {/* Display total entries info */}
        <EntryInfo>Total of {filteredData.length} entries</EntryInfo>

        {/* Main table */}
        <TableContainer>
          <StyledTable>
            <TableHeader
              columns={columns}
              sortConfig={sortConfig}
              onSort={handleSort}
              onSelectAll={toggleSelectAll}
              allSelected={
                getCurrentPageData().length > 0 &&
                getCurrentPageData().every((row) => selectedRows.has(row.id))
              }
            />
            <tbody>
              {getCurrentPageData().map((row, index) => (
                <TableRow
                  key={row.id || index}
                  rowData={row}
                  columns={columns}
                  selected={selectedRows.has(row.id)}
                  onSelect={() => toggleSelectRow(row.id)}
                  onClick={() => {
                    setOpenPeakDrawer(false);
                    setSelectedStudent(row);
                    setOpenPeakDrawer(true);
                  }}
                />
              ))}
            </tbody>
          </StyledTable>
        </TableContainer>

        {/* Pagination controls */}
        <PaginationContainer>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </PaginationContainer>
      </Container>

      <PeakDetails
        student={selectedStudent}
        isOpen={openPeakDrawer}
        onClose={() => setOpenPeakDrawer(false)}
      />
    </>
  );
};

export default DataTable;
