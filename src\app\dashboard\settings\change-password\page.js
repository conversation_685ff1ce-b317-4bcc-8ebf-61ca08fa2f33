// app/dashboard/settings/change-password/page.js
"use client";
import React, { useState } from "react";
import { Eye, EyeOff, Lock, CheckCircle, XCircle } from "lucide-react";
import {
  Button,
  ButtonGroup,
  Container,
  Description,
  FormGroup,
  IconWrapper,
  Input,
  InputWrapper,
  Label,
  Required,
  ToggleButton,
  ValidationItem,
  ValidationList,
  ValidationTitle,
} from "@/components/styles/dashboard/changePassword.styled";
import { useMutation } from "@tanstack/react-query";
import { api, mutations } from "@/api/client";
import toast from "react-hot-toast";

const ChangePassword = () => {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const [validations, setValidations] = useState({
    hasUppercase: false,
    hasNumber: false,
    hasMinLength: false,
    passwordsMatch: false,
  });

  // Fix: Use the mutations object directly instead of queries
  const changePasswordMutation = useMutation({
    ...mutations.changePassword,
    onSuccess: () => {
      // Reset form after successful password change
      handleDiscard();
    },
    onError: (error) => {
      console.error("Password change error:", error);
      toast.error(
        error?.response?.data?.details || "Failed to change password"
      );
    },
  });

  const validatePassword = (password) => {
    setValidations({
      hasUppercase: /[A-Z]/.test(password),
      hasNumber: /\d/.test(password),
      hasMinLength: password.length >= 8,
      passwordsMatch: password === formData.confirmPassword,
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    if (name === "newPassword") {
      validatePassword(value);
    }
    if (name === "confirmPassword") {
      setValidations((prev) => ({
        ...prev,
        passwordsMatch: value === formData.newPassword,
      }));
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate passwords match before submission
    if (formData.newPassword !== formData.confirmPassword) {
      toast.error("New password and confirmation do not match");
      return;
    }

    // Check if all validations pass
    if (!isFormValid) {
      toast.error("Please ensure your password meets all requirements");
      return;
    }

    // Call the API to change password
    // Fix: Make sure we're sending the correct payload format
    changePasswordMutation.mutate({
      currentPassword: formData.currentPassword,
      newPassword: formData.newPassword,
      confirmPassword: formData.confirmPassword,
    });
  };

  const handleDiscard = () => {
    setFormData({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
    setValidations({
      hasUppercase: false,
      hasNumber: false,
      hasMinLength: false,
      passwordsMatch: false,
    });
  };

  const isFormValid = Object.values(validations).every((v) => v);

  return (
    <Container>
      <Description>Update password for enhanced account security.</Description>

      <form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>
            Current Password
            <Required>*</Required>
          </Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={17} />
            </IconWrapper>
            <Input
              type={showPasswords.current ? "text" : "password"}
              name="currentPassword"
              value={formData.currentPassword}
              onChange={handleInputChange}
              required
            />
            <ToggleButton
              type="button"
              onClick={() => togglePasswordVisibility("current")}
            >
              {showPasswords.current ? <EyeOff size={20} /> : <Eye size={20} />}
            </ToggleButton>
          </InputWrapper>
        </FormGroup>

        <FormGroup>
          <Label>
            New Password
            <Required>*</Required>
          </Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={17} />
            </IconWrapper>
            <Input
              type={showPasswords.new ? "text" : "password"}
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              required
            />
            <ToggleButton
              type="button"
              onClick={() => togglePasswordVisibility("new")}
            >
              {showPasswords.new ? <EyeOff size={20} /> : <Eye size={20} />}
            </ToggleButton>
          </InputWrapper>
        </FormGroup>

        <FormGroup>
          <Label>
            Confirm New Password
            <Required>*</Required>
          </Label>
          <InputWrapper>
            <IconWrapper>
              <Lock size={17} />
            </IconWrapper>
            <Input
              type={showPasswords.confirm ? "text" : "password"}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              required
            />
            <ToggleButton
              type="button"
              onClick={() => togglePasswordVisibility("confirm")}
            >
              {showPasswords.confirm ? <EyeOff size={20} /> : <Eye size={20} />}
            </ToggleButton>
          </InputWrapper>
        </FormGroup>

        <ValidationList>
          <ValidationTitle>Must contain at least:</ValidationTitle>
          <ValidationItem isValid={validations.hasUppercase}>
            {validations.hasUppercase ? (
              <CheckCircle size={16} />
            ) : (
              <XCircle size={16} />
            )}
            At least 1 uppercase
          </ValidationItem>
          <ValidationItem isValid={validations.hasNumber}>
            {validations.hasNumber ? (
              <CheckCircle size={16} />
            ) : (
              <XCircle size={16} />
            )}
            At least 1 number
          </ValidationItem>
          <ValidationItem isValid={validations.hasMinLength}>
            {validations.hasMinLength ? (
              <CheckCircle size={16} />
            ) : (
              <XCircle size={16} />
            )}
            At least 8 characters
          </ValidationItem>
        </ValidationList>

        <ButtonGroup>
          <Button type="button" variant="secondary" onClick={handleDiscard}>
            Discard
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={!isFormValid || changePasswordMutation.isPending}
          >
            {changePasswordMutation.isPending ? "Changing..." : "Apply Changes"}
          </Button>
        </ButtonGroup>
      </form>
    </Container>
  );
};

export default ChangePassword;
