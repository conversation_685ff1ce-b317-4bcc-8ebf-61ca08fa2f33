import styled, { css } from "styled-components";

// Base Button Component
export const BaseButton = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 14px;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

export const Button = styled(BaseButton)`
  ${(props) => {
    switch (props.variant) {
      case "outline":
        return css`
          border: 1px solid #e2e8f0;
          background: transparent;
          color: #1a202c;
          padding: 8px 16px;
          &:hover {
            background: #f7fafc;
          }
        `;
      case "ghost":
        return css`
          background: transparent;
          color: #4a5568;
          padding: ${props.size === "icon" ? "8px" : "8px 16px"};
          &:hover {
            background: #f7fafc;
          }
        `;
      default:
        return css`
          background: #3182ce;
          color: white;
          border: none;
          padding: 8px 16px;
          &:hover {
            background: #2c5282;
          }
        `;
    }
  }}
  a {
    color: #333;
    text-decoration: none; /* Remove underline */
    width: 100%;
    text-align: center;
  }
`;

// Icon Components
export const Icon = styled.svg`
  width: ${(props) => props.size || "16px"};
  height: ${(props) => props.size || "16px"};
`;

export const PencilIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
    />
  </Icon>
);

export const DownloadIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
    />
  </Icon>
);

export const TrashIcon = (props) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
    />
  </Icon>
);

// Layout Components
export const Container = styled.div`
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 2rem;
`;

export const ContentWrapper = styled.div`
  max-width: 72rem;
  margin: 0 auto;
`;

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

export const MainTitle = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
`;

export const MainContent = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

export const FlexContainer = styled.div`
  display: flex;
  gap: 3rem;
`;

// Sidebar Components
export const Nav = styled.nav`
  width: 12rem;
  border-right: 1px solid #f0f0f0; // Added right border
`;

export const NavTitle = styled.div`
  font-weight: 500;
  margin-bottom: 1rem;

  a {
    text-decoration: none;
    color: ${(props) =>
      props.$active ? "#059669" : "#4b5563"}; /* Green when active */
    font-weight: ${(props) => (props.$active ? "600" : "500")};
    border-bottom: ${(props) =>
      props.$active ? "2px solid #059669" : "none"}; /* Underline when active */
    padding-bottom: 4px;
    display: inline-block;
  }

  &:hover a {
    color: #059669; /* Green on hover */
  }
`;

export const NavList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
`;

export const NavItem = styled.li`
  color: #4b5563;
  cursor: pointer;

  &:hover {
    color: #1a202c;
  }
`;

// Avatar Component
export const Avatar = styled.div`
  width: 4rem;
  height: 4rem;
  background-color: #fee2e2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
`;

// Profile Components
export const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
`;

export const ProfileInfo = styled.div`
  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 0.25rem 0;
  }
  p {
    color: #6b7280;
    margin: 0;
  }
`;

// Details Components
export const DetailsContainer = styled.div`
  margin-top: 2rem;
`;

export const DetailsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

export const DetailsTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
`;

export const DetailGroup = styled.div`
  margin-bottom: 1.5rem;
`;

export const Label = styled.p`
  color: #6b7280;
  margin: 0 0 0.25rem 0;
`;

export const Value = styled.p`
  font-weight: 500;
  color: #1a202c;
  margin: 0;
`;

// File Components
export const FileContainer = styled.div`
  margin-top: 2rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

export const FileType = styled.div`
  background-color: #fee2e2;
  padding: 0.5rem;
  border-radius: 0.25rem;

  span {
    color: #dc2626;
    font-size: 0.875rem;
    font-weight: 500;
  }
`;

export const FileDetails = styled.div`
  p {
    margin: 0;

    &:first-child {
      font-weight: 500;
      color: #1a202c;
    }

    &:last-child {
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
`;

export const FileActions = styled.div`
  display: flex;
  gap: 0.5rem;
`;
