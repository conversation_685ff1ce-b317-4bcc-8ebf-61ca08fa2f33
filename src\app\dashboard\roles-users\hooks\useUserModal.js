import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { mutations } from "@/api/client";

export const useUserModal = () => {
  const queryClient = useQueryClient();
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    role: "",
    fullName: "",
    email: "",
    phoneNumber: "",
    region: "",
    district: null,
  });

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // If the role changes to nss_officer and we have a region, fetch districts
    if (name === "role" && value === "nss_officer" && formData.region) {
      queryClient.invalidateQueries({
        queryKey: ["districts"],
      });
    }

    // If the region changes and the role is already nss_officer, fetch districts
    if (name === "region" && formData.role === "nss_officer") {
      queryClient.invalidateQueries({
        queryKey: ["districts"],
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      role: "",
      fullName: "",
      email: "",
      phoneNumber: "",
      region: "",
      district: null,
    });
  };

  // Add administrator mutation
  const addAdministratorMutation = useMutation({
    ...mutations.addAdministrators,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["administrators"] });
      setShowAddUserModal(false);
      resetForm();
      toast.success("Administrator added successfully!");
    },
    onError: (error) => {
      toast.error(
        error?.response?.data?.message || "Failed to add administrator"
      );
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const administrator = {
      role: formData.role,
      fullName: formData.fullName,
      email: formData.email,
      phoneNumber: formData.phoneNumber,
      region:
        formData.role === "regional_administrator" ||
        formData.role === "regional_director" ||
        formData.role === "nss_officer"
          ? formData.region
          : null,
      district: formData.role === "nss_officer" ? formData.district : null,
    };

    addAdministratorMutation.mutate(administrator);
  };

  return {
    showAddUserModal,
    setShowAddUserModal,
    isSubmitting,
    formData,
    setFormData,
    handleInputChange,
    handleSubmit,
    resetForm,
  };
};
