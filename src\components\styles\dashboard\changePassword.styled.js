import styled from "styled-components";

export const Container = styled.div`
  max-width: 42rem;
`;

export const Title = styled.h1`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
`;

export const Description = styled.p`
  font-size: 1.125rem;
  color: #374151;
  margin-bottom: 14px;
`;

export const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

export const Label = styled.label`
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
`;

export const Required = styled.span`
  color: #22c55e;
  margin-left: 0.25rem;
`;

export const InputWrapper = styled.div`
  width: 80%;
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: white;
  margin-bottom: 20px;
`;

export const Input = styled.input`
  width: 95%;
  padding: 12px 10px 12px 35px; // Added left padding for icon
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: #006600;
    box-shadow: 0 0 0 1px #006600;
  }
`;

export const IconWrapper = styled.div`
  position: absolute;
  left: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  font-size: 15px;
`;

export const ToggleButton = styled.span`
  position: absolute;
  right: 12px;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 18px;
  transition: color 0.2s ease-in-out;

  &:hover {
    color: #374151;
  }
`;

export const ValidationList = styled.div`
  margin-top: 1.5rem;
`;

export const ValidationTitle = styled.p`
  color: #6b7280;
  margin-bottom: 0.5rem;
`;

export const ValidationItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: ${(props) => (props.isValid ? "#22c55e" : "#6b7280")};
  margin-bottom: 0.25rem;
`;

export const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
`;

export const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s;

  ${(props) =>
    props.variant === "primary" &&
    `
    background-color: #000;
    color: white;
    border: none;

    &:hover {
      opacity: 0.9;
    }

    &:disabled {
      background-color: #9ca3af;
      cursor: not-allowed;
    }
  `}

  ${(props) =>
    props.variant === "secondary" &&
    `
    background-color: white;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #f3f4f6;
    }
  `}
`;
