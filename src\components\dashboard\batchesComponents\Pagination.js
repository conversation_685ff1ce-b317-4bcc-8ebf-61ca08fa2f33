// components/Pagination.jsx
import React from "react";
import styled from "styled-components";
import { ChevronLeft, ChevronRight } from "lucide-react";

const PaginationContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1rem 0;
  margin-top: 1.5rem;
  gap: 1rem;
`;

const PageInfo = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
`;

const NavigationButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background: white;
  cursor: pointer;
  color: #6b7280;
  
  &:hover {
    background: #f9fafb;
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
`;

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  if (totalPages <= 1) return null;

  return (
    <PaginationContainer>
      <NavigationButton
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft size={20} />
      </NavigationButton>

      <PageInfo>
        {currentPage} / {totalPages} Page
      </PageInfo>

      <NavigationButton
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <ChevronRight size={20} />
      </NavigationButton>
    </PaginationContainer>
  );
};

export default Pagination;
