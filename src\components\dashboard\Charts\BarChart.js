import React from "react";
import styled from "styled-components";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, ResponsiveContainer, Toolt<PERSON> } from "recharts";

const ChartContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
`;

const ChartHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
`;

const ChartIcon = styled.div`
  margin-right: 12px;
  color: #6b7280;
`;

const ChartTitle = styled.h3`
  font-size: 20px;
  font-weight: 500;
  color: #111827;
  margin: 0;
`;

const ViewMoreButton = styled.button`
  background-color: #f3f4f6;
  color: #374151;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;

  &:hover {
    background-color: #e5e7eb;
  }
`;

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div
        style={{
          backgroundColor: "#fff",
          padding: "10px",
          border: "1px solid #ccc",
          borderRadius: "4px",
        }}
      >
        <p style={{ margin: 0 }}>{`${label}: ${payload[0].value} students`}</p>
      </div>
    );
  }
  return null;
};

const StudentsSubmittedChart = ({ onViewMoreClick }) => {
  const data = [
    { name: "Jan", value: 186 },
    { name: "Feb", value: 360 },
    { name: "Mar", value: 305 },
    { name: "Apr", value: 78 },
    { name: "May", value: 237 },
    { name: "Jun", value: 305 },
    { name: "Jul", value: 305 },
    { name: "Aug", value: 73 },
    { name: "Sep", value: 53 },
    { name: "Oct", value: 300 },
    { name: "Nov", value: 200 },
    { name: "Dec", value: 214 },
  ];

  return (
    <ChartContainer>
      <ChartHeader>
        <ChartIcon>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 13V17M16 11V17M12 7V17M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </ChartIcon>
        <ChartTitle>Students Submitted</ChartTitle>
      </ChartHeader>

      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          margin={{ top: 20, right: 30, left: 0, bottom: 5 }}
        >
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#6b7280", fontSize: 12 }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar
            dataKey="value"
            fill="#0A9F1C"
            radius={[4, 4, 0, 0]}
            barSize={30}
          />
        </BarChart>
      </ResponsiveContainer>

      <ViewMoreButton onClick={onViewMoreClick}>
        View More Analytics
      </ViewMoreButton>
    </ChartContainer>
  );
};

export default StudentsSubmittedChart;
