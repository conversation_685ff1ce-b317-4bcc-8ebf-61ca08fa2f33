"use client";

import { useParams, useRouter } from "next/navigation";
import styled from "styled-components";
import Image from "next/image";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { queries, api } from "@/api/client";
import { ChevronLeft, Download, Printer, MoreVertical } from "lucide-react";
import { useState, useEffect, useRef } from "react";

export default function InstitutionDetails() {
  const params = useParams();
  const { push } = useRouter();
  const institutionId = params.institutionId;
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const queryClient = useQueryClient();
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  // Add the approve institution mutation
  const { mutate: approveInstitution, isLoading: isApproveLoading } =
    useMutation({
      mutationFn: () =>
        api.approveInstitution({
          institutionId,
          approved: true,
          approvalStatus: "APPROVED",
        }),
      onSuccess: () => {
        // Invalidate and refetch the institution data
        queryClient.invalidateQueries(["institution", institutionId]);
        setIsApproving(false);
      },
      onError: (error) => {
        console.error("Error approving institution:", error);
        setIsApproving(false);
      },
    });

  // Add the reject institution mutation
  const { mutate: rejectInstitution, isLoading: isRejectLoading } = useMutation(
    {
      mutationFn: () =>
        api.approveInstitution({
          institutionId,
          approved: false,
          approvalStatus: "REJECTED",
        }),
      onSuccess: () => {
        // Invalidate and refetch the institution data
        queryClient.invalidateQueries(["institution", institutionId]);
        setIsRejecting(false);
      },
      onError: (error) => {
        console.error("Error rejecting institution:", error);
        setIsRejecting(false);
      },
    }
  );

  // Handle approve button click
  const handleApprove = () => {
    setIsApproving(true);
    approveInstitution();
  };

  // Handle reject button click
  const handleReject = () => {
    setIsRejecting(true);
    setShowRejectModal(false);
    rejectInstitution();
  };

  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Update the useQuery hook in the component
  const {
    data: institutionData,
    isLoading,
    error,
  } = useQuery({
    ...queries.getInstitution(institutionId),
    retry: 1,
    onError: (error) => {
      if (error?.response?.status === 401) {
        sessionStorage.removeItem("authToken");
        push("/login");
      }
    },
    enabled: !!sessionStorage.getItem("authToken"),
  });

  // Then update how we access the institution data
  const institution = institutionData?.data;

  console.log("Institution data:", institution);
  console.log("Approval status:", institution?.approvalStatus);
  console.log(
    "Needs approval:",
    institution?.approvalStatus === "PENDING" ||
      institution?.approvalStatus === "pending"
  );

  if (isLoading) {
    return (
      <Container>
        <LoaderContainer>
          <LoaderSpinner />
          <LoaderText>Loading institution details...</LoaderText>
        </LoaderContainer>
      </Container>
    );
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  // Check if institution needs approval based on the approvalStatus property
  const needsApproval =
    institution?.approvalStatus === "PENDING" ||
    institution?.approvalStatus === "pending" ||
    institution?.approved === false;

  return (
    <Container>
      <ActionContainer>
        <PageTitle>View Institution Details</PageTitle>
        <ButtonGroup>
          {needsApproval && (
            <>
              <ActionButton
                variant="approve"
                onClick={handleApprove}
                disabled={isApproveLoading || isApproving}
              >
                {isApproveLoading || isApproving ? "Approving..." : "Approve"}
              </ActionButton>
              <ActionButton
                variant="reject"
                onClick={() => setShowRejectModal(true)}
                disabled={isRejectLoading || isRejecting}
              >
                {isRejectLoading || isRejecting ? "Rejecting..." : "Reject"}
              </ActionButton>
            </>
          )}
          <DropdownContainer ref={dropdownRef}>
            <ActionButton
              variant="more"
              onClick={(e) => {
                e.stopPropagation();
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <MoreVertical size={16} />
            </ActionButton>
            {dropdownOpen && (
              <DropdownMenu>
                <DropdownItem
                  onClick={() => {
                    /* Add PDF download logic */
                    setDropdownOpen(false);
                  }}
                >
                  <Download size={16} />
                  Download as PDF
                </DropdownItem>
                <DropdownItem
                  onClick={() => {
                    window.print();
                    setDropdownOpen(false);
                  }}
                >
                  <Printer size={16} />
                  Print Page
                </DropdownItem>
              </DropdownMenu>
            )}
          </DropdownContainer>
        </ButtonGroup>
      </ActionContainer>
      <BackButtonContainer>
        <BackButton onClick={() => push("/dashboard/institutions")}>
          <ChevronLeft size={16} />
          Back
        </BackButton>
      </BackButtonContainer>

      <MainContent>
        <SectionHeader>UPLOADED DETAILS</SectionHeader>

        <InstitutionHeaderSection>
          <InstitutionLogo>
            <Image
              src="/profile-pic-placeholder.svg"
              alt="Institution Logo"
              width={80}
              height={80}
              style={{ objectFit: "contain" }}
            />
          </InstitutionLogo>
          <InstitutionHeaderInfo>
            <InstitutionLabel>Institution Name</InstitutionLabel>
            <InstitutionName>
              {institution?.name || "Legon University"}
            </InstitutionName>
          </InstitutionHeaderInfo>
        </InstitutionHeaderSection>

        <SectionHeader>OTHER DETAILS</SectionHeader>
        <DetailsGrid>
          <DetailItem>
            <DetailLabel>Institution Email</DetailLabel>
            <DetailValue>{institution?.email || "<EMAIL>"}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Institution Address</DetailLabel>
            <DetailValue>
              {institution?.address || "Greenhill, Accra"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Type of Institution</DetailLabel>
            <DetailValue>
              {institution?.category || "Greenhill, Accra"}
            </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Number of Courses</DetailLabel>
            <DetailValue> {institution?.numberOfCourses} </DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Number of Students Submitted</DetailLabel>
            <DetailValue>{institution?.numberOfStudents}</DetailValue>
          </DetailItem>
          <DetailItem>
            <DetailLabel>Date Registered</DetailLabel>
            <DetailValue>
              {institution?.createdAt
                ? new Date(institution.createdAt).toISOString().split("T")[0]
                : ""}
            </DetailValue>
          </DetailItem>
        </DetailsGrid>

        <SectionHeader>DOCUMENTS</SectionHeader>
        <DocumentSection>
          <DocumentLabel>Uploaded Document</DocumentLabel>
          <DocumentItem>
            <DocumentIconContainer>
              <DocumentIcon>PDF</DocumentIcon>
            </DocumentIconContainer>
            <DocumentName>
              {institution?.documents ? institution.documents : "Documents.pdf"}
            </DocumentName>
            <DownloadButton
              onClick={() =>
                institution?.documents &&
                window.open(institution.documents, "_blank")
              }
              style={{
                cursor: institution?.documents ? "pointer" : "not-allowed",
                opacity: institution?.documents ? 1 : 0.5,
              }}
            >
              <Download size={16} />
            </DownloadButton>
          </DocumentItem>
        </DocumentSection>
      </MainContent>

      {/* Confirmation Modal */}
      {showRejectModal && (
        <ModalOverlay>
          <ModalContainer>
            <ModalHeader>Confirm Rejection</ModalHeader>
            <ModalContent>
              Are you sure you want to reject this institution?
            </ModalContent>
            <ModalActions>
              <ModalButton
                variant="cancel"
                onClick={() => setShowRejectModal(false)}
              >
                Cancel
              </ModalButton>
              <ModalButton variant="reject" onClick={handleReject}>
                Reject
              </ModalButton>
            </ModalActions>
          </ModalContainer>
        </ModalOverlay>
      )}
    </Container>
  );
}

const Container = styled.div`
  min-height: 100vh;
  padding-bottom: 2rem;
  margin: 0 auto;
  background-color: #f5f7fa;
`;

const BackButtonContainer = styled.div`
  padding: 1rem 2rem;
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: #000;
  padding: 0;
`;

const MainContent = styled.main`
  margin: 0 auto;
  padding: 0 2rem;
`;

const SectionHeader = styled.h2`
  font-size: 14px;
  font-weight: 600;
  color: #333;
  background-color: #f0f2f5;
  padding: 1rem;
  margin: 1.5rem 0 1rem 0;
  border-radius: 4px;
`;

const InstitutionHeaderSection = styled.div`
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const InstitutionLogo = styled.div`
  width: 80px;
  height: 80px;
  flex-shrink: 0;
`;

const InstitutionHeaderInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const InstitutionLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.25rem;
`;

const InstitutionName = styled.h1`
  font-size: 24px;
  font-weight: 500;
  color: #000;
  margin: 0;
`;

const DetailsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 2rem;
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 0.5rem;
`;

const DetailValue = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: #000;
`;

const DocumentSection = styled.div`
  margin: 1.5rem 0;
  padding: 2rem 1rem;
  background-color: white;
  border-radius: 4px;
`;

const DocumentLabel = styled.div`
  font-size: 14px;
  color: #666;
  margin-bottom: 1rem;
`;

const DocumentItem = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 0.5rem;
`;

const DocumentIconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const DocumentIcon = styled.div`
  background-color: #ff4d4f;
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 12px;
  border-radius: 2px;
`;

const DocumentName = styled.div`
  font-size: 16px;
  flex-grow: 1;
`;

const DownloadButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  &:hover {
    color: #000;
  }
`;

const ActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  border-bottom: 1px solid #e1e4ea;
`;

const PageTitle = styled.h1`
  font-size: 20px;
  font-weight: 500;
  color: #000;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  ${(props) =>
    props.variant === "approve" &&
    `
    background-color: #119411;
    border: none;
    color: #fff;
  `}

  ${(props) =>
    props.variant === "reject" &&
    `
    background-color: #ff4d4f;
    color: #fff;
    border: none;
  `}
  
  ${(props) =>
    props.variant === "more" &&
    `
    background: none;
    border: none;
    color: #666;
    padding: 0.5rem;
    &:hover {
      background-color: #f5f5f5;
    }
  `}
`;

const DropdownContainer = styled.div`
  position: relative;
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background-color: white;
  border: 1px solid #e1e4ea;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 1000;
`;

const DropdownItem = styled.button`
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
  }
`;

// Add these styled components after your existing styled components
const LoaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
`;

const LoaderSpinner = styled.div`
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #16a34a 94%, #0000) top/9px 9px
      no-repeat,
    conic-gradient(#0000 30%, #16a34a);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 9px), #000 0);
  animation: spinner-animation 1s infinite linear;
  margin-bottom: 1.5rem;

  @keyframes spinner-animation {
    100% {
      transform: rotate(1turn);
    }
  }
`;

const LoaderText = styled.div`
  color: #6b7280;
  font-size: 1rem;
  font-weight: 500;
`;

// Modal styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #e1e4ea;
  font-weight: 600;
  font-size: 18px;
`;

const ModalContent = styled.div`
  padding: 1.5rem 1rem;
`;

const ModalActions = styled.div`
  display: flex;
  justify-content: flex-end;
  padding: 1rem;
  gap: 0.5rem;
  border-top: 1px solid #e1e4ea;
`;

const ModalButton = styled.button`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;

  ${(props) =>
    props.variant === "cancel" &&
    `
    background-color: #f5f5f5;
    border: 1px solid #e1e4ea;
    color: #333;
  `}

  ${(props) =>
    props.variant === "reject" &&
    `
    background-color: #ff4d4f;
    border: none;
    color: white;
  `}
`;
